<script setup lang="ts">
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref } from "vue";
import { getRoles } from "../utils/auth-api";
import { DateRangePicker } from "@/components/Shared";
import type { UserFilterProps } from "../utils/type";

interface Emits {
  (e: "search", data: UserFilterProps): void;
  (e: "reset"): void;
}

const emit = defineEmits<Emits>();

const formRef = ref();
const roles = ref([]);

const formData = ref<UserFilterProps>({
  name: "",
  email: "",
  status: "",
  role_id: null,
  email_verified: null,
  created_at: null,
  last_login_at: null
});

const statusOptions = [
  { label: $t("All Status"), value: "" },
  { label: $t("Active"), value: "active" },
  { label: $t("Inactive"), value: "inactive" },
  { label: $t("Suspended"), value: "suspended" },
  { label: $t("Banned"), value: "banned" },
  { label: $t("Pending"), value: "pending" }
];

const emailVerifiedOptions = [
  { label: $t("All"), value: null },
  { label: $t("Verified"), value: true },
  { label: $t("Unverified"), value: false }
];

const roleOptions = computed(() => [
  { label: $t("All Roles"), value: null },
  ...roles.value.map((role: any) => ({
    label: role.display_name || role.name,
    value: role.id
  }))
]);

const loadRoles = async () => {
  try {
    const { data } = await getRoles();
    if (data?.success) {
      roles.value = data.data || [];
    }
  } catch (error) {
    console.error("Error loading roles:", error);
  }
};

const handleSearch = () => {
  const searchData = { ...formData.value };
  
  // Remove empty values
  Object.keys(searchData).forEach(key => {
    if (searchData[key] === "" || searchData[key] === null) {
      delete searchData[key];
    }
  });
  
  emit("search", searchData);
};

const handleReset = () => {
  formData.value = {
    name: "",
    email: "",
    status: "",
    role_id: null,
    email_verified: null,
    created_at: null,
    last_login_at: null
  };
  
  emit("reset");
};

onMounted(() => {
  loadRoles();
});
</script>

<template>
  <el-form
    ref="formRef"
    :model="formData"
    label-width="120px"
    label-position="right"
    class="user-filter-form"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item :label="$t('Name')">
          <el-input
            v-model="formData.name"
            :placeholder="$t('Enter user name')"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item :label="$t('Email')">
          <el-input
            v-model="formData.email"
            :placeholder="$t('Enter email address')"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item :label="$t('Status')">
          <el-select
            v-model="formData.status"
            :placeholder="$t('Select status')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item :label="$t('Role')">
          <el-select
            v-model="formData.role_id"
            :placeholder="$t('Select role')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in roleOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item :label="$t('Email Verified')">
          <el-select
            v-model="formData.email_verified"
            :placeholder="$t('Select verification status')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in emailVerifiedOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item :label="$t('Registration Date')">
          <DateRangePicker
            v-model="formData.created_at"
            :placeholder="$t('Select registration date range')"
            class="w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item :label="$t('Last Login')">
          <DateRangePicker
            v-model="formData.last_login_at"
            :placeholder="$t('Select last login date range')"
            class="w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item>
      <div class="flex justify-end space-x-2 w-full">
        <el-button @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button type="primary" @click="handleSearch">
          {{ $t("Search") }}
        </el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<style scoped>
.user-filter-form {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-select .el-input__wrapper) {
  cursor: pointer;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
