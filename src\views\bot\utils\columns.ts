import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag, ElAvatar } from "element-plus";
import { h } from "vue";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "logo",
    align: "center",
    width: 60,
    headerRenderer: () => $t("Logo"),
    cellRenderer: ({ row }) => {
      return h(ElAvatar, {
        size: 40,
        src: row.logo,
        alt: row.name
      });
    }
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Bot Name")
  },
  {
    prop: "description",
    align: "left",
    minWidth: 200,
    headerRenderer: () => $t("Description"),
    cellRenderer: ({ row }) => {
      const desc = row.description || "-";
      return desc.length > 50 ? desc.substring(0, 50) + "..." : desc;
    }
  },
  {
    prop: "aiModel",
    align: "left",
    width: 140,
    headerRenderer: () => $t("AI Model"),
    cellRenderer: ({ row }) => row.aiModel?.name || "-"
  },
  {
    prop: "toolCallingMode",
    align: "center",
    width: 120,
    headerRenderer: () => $t("Tool Mode"),
    cellRenderer: ({ row }) => {
      const modeColors = {
        auto: "primary",
        none: "info",
        required: "warning"
      };
      return h(
        ElTag,
        {
          type: modeColors[row.toolCallingMode] || "info",
          size: "small"
        },
        () => row.toolCallingMode?.toUpperCase() || "NONE"
      );
    }
  },
  {
    prop: "visibility",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Visibility"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.visibility === "public" ? "success" : "warning",
          size: "small"
        },
        () => row.visibility?.toUpperCase() || "PRIVATE"
      );
    }
  },
  {
    prop: "status",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: "info",
        review: "warning", 
        active: "success",
        paused: "warning",
        banned: "danger"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status] || "info",
          size: "small"
        },
        () => row.status?.toUpperCase() || "DRAFT"
      );
    }
  },
  {
    prop: "createdAt",
    align: "center",
    width: 120,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) =>
      dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
  },
  {
    prop: "operation",
    fixed: "right",
    width: 100,
    slot: "operation",
    headerRenderer: () => $t("Operation")
  }
];
