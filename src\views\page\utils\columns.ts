import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "locale",
    align: "center",
    width: 80,
    headerRenderer: () => $t("Locale"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: "info",
          size: "small"
        },
        () => row.locale?.toUpperCase()
      );
    }
  },
  {
    prop: "title",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Title")
  },
  {
    prop: "slug",
    align: "left",
    sortable: true,
    minWidth: 140,
    headerRenderer: () => $t("Slug")
  },
  {
    prop: "status",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: "info",
        published: "success",
        archived: "warning"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status] || "info",
          size: "small"
        },
        () => row.status?.toUpperCase()
      );
    }
  },
  {
    prop: "authorId",
    align: "center",
    width: 120,
    headerRenderer: () => $t("Author"),
    cellRenderer: ({ row }) => row.author?.name || "-"
  },
  {
    prop: "publishedAt",
    width: 160,
    headerRenderer: () => $t("Published at"),
    formatter: ({ publishedAt }) =>
      publishedAt ? dayjs(publishedAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    prop: "createdAt",
    width: 160,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
