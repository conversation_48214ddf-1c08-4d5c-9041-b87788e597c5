<script setup lang="ts">
import { computed } from "vue";
import { $t } from "@/plugins/i18n";
import { ElAvatar, ElTooltip } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import dayjs from "dayjs";
import type { ChatMessage } from "@/services/websocket";

interface Props {
  message: ChatMessage;
  isOwn: boolean;
  user?: {
    id: number;
    name: string;
    avatar?: string;
  };
  bot?: {
    id: number;
    name: string;
    logo?: string;
  };
}

interface Emits {
  (e: "retry", messageId: string): void;
  (e: "mark-read", messageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const messageTime = computed(() => {
  return dayjs(props.message.timestamp).format("HH:mm");
});

const messageDate = computed(() => {
  return dayjs(props.message.timestamp).format("YYYY-MM-DD HH:mm");
});

const statusIcon = computed(() => {
  const iconMap = {
    sending: "ri/time-line",
    sent: "ri/check-line",
    delivered: "ri/check-double-line",
    read: "ri/check-double-line",
    failed: "ri/error-warning-line"
  };
  return iconMap[props.message.status] || "ri/check-line";
});

const statusColor = computed(() => {
  const colorMap = {
    sending: "text-gray-400",
    sent: "text-gray-500",
    delivered: "text-blue-500",
    read: "text-blue-600",
    failed: "text-red-500"
  };
  return colorMap[props.message.status] || "text-gray-500";
});

const avatarData = computed(() => {
  if (props.isOwn) {
    return {
      src: props.user?.avatar,
      name: props.user?.name || "User"
    };
  } else {
    return {
      src: props.bot?.logo,
      name: props.bot?.name || "Bot"
    };
  }
});

const bubbleClass = computed(() => {
  const baseClass = "message-bubble";
  const alignClass = props.isOwn ? "own-message" : "other-message";
  const statusClass = props.message.status === "failed" ? "failed-message" : "";
  return [baseClass, alignClass, statusClass].filter(Boolean).join(" ");
});

const handleRetry = () => {
  if (props.message.status === "failed") {
    emit("retry", props.message.id);
  }
};

const handleMarkRead = () => {
  if (!props.isOwn && props.message.status !== "read") {
    emit("mark-read", props.message.id);
  }
};
</script>

<template>
  <div :class="['message-wrapper', isOwn ? 'own' : 'other']">
    <!-- Avatar (for other messages) -->
    <div v-if="!isOwn" class="message-avatar">
      <el-avatar :size="32" :src="avatarData.src" :alt="avatarData.name">
        {{ avatarData.name.charAt(0).toUpperCase() }}
      </el-avatar>
    </div>

    <!-- Message Content -->
    <div class="message-content">
      <!-- Sender name (for other messages) -->
      <div v-if="!isOwn" class="sender-name">
        {{ avatarData.name }}
      </div>

      <!-- Message Bubble -->
      <div :class="bubbleClass" @click="handleMarkRead">
        <!-- Text Content -->
        <div v-if="message.contentType === 'text'" class="message-text">
          {{ message.content }}
        </div>

        <!-- Image Content -->
        <div v-else-if="message.contentType === 'image'" class="message-image">
          <img
            :src="message.content"
            :alt="$t('Image message')"
            class="max-w-xs rounded-lg"
            loading="lazy"
          />
        </div>

        <!-- File Content -->
        <div v-else-if="message.contentType === 'file'" class="message-file">
          <div class="flex items-center space-x-2 p-3 bg-gray-100 rounded-lg">
            <IconifyIconOffline
              :icon="useRenderIcon('ri/file-line')"
              class="text-xl text-gray-600"
            />
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium truncate">
                {{ message.content }}
              </div>
              <div class="text-xs text-gray-500">
                {{ $t("File attachment") }}
              </div>
            </div>
            <el-button type="text" size="small" @click.stop="() => {}">
              <IconifyIconOffline :icon="useRenderIcon('ri/download-line')" />
            </el-button>
          </div>
        </div>

        <!-- Audio Content -->
        <div v-else-if="message.contentType === 'audio'" class="message-audio">
          <div class="flex items-center space-x-2 p-3 bg-gray-100 rounded-lg">
            <IconifyIconOffline
              :icon="useRenderIcon('ri/volume-up-line')"
              class="text-xl text-gray-600"
            />
            <div class="flex-1">
              <audio controls class="w-full">
                <source :src="message.content" type="audio/mpeg" />
                {{ $t("Your browser does not support audio playback") }}
              </audio>
            </div>
          </div>
        </div>

        <!-- Attachments -->
        <div
          v-if="message.attachments && message.attachments.length > 0"
          class="message-attachments"
        >
          <div
            v-for="attachment in message.attachments"
            :key="attachment.id"
            class="attachment-item"
          >
            <IconifyIconOffline :icon="useRenderIcon('ri/attachment-line')" />
            <span class="text-sm">{{ attachment.name }}</span>
          </div>
        </div>

        <!-- Message Status & Time -->
        <div class="message-meta">
          <span class="message-time">{{ messageTime }}</span>

          <!-- Status for own messages -->
          <div v-if="isOwn" class="message-status">
            <el-tooltip :content="$t(message.status)" placement="top">
              <IconifyIconOffline
                :icon="useRenderIcon(statusIcon)"
                :class="['status-icon', statusColor]"
              />
            </el-tooltip>

            <!-- Retry button for failed messages -->
            <el-button
              v-if="message.status === 'failed'"
              type="text"
              size="small"
              class="retry-button"
              @click.stop="handleRetry"
            >
              <IconifyIconOffline :icon="useRenderIcon('ri/refresh-line')" />
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Avatar (for own messages) -->
    <div v-if="isOwn" class="message-avatar">
      <el-avatar :size="32" :src="avatarData.src" :alt="avatarData.name">
        {{ avatarData.name.charAt(0).toUpperCase() }}
      </el-avatar>
    </div>
  </div>
</template>

<style scoped>
.message-wrapper {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-end;
}

.message-wrapper.own {
  flex-direction: row-reverse;
}

.message-wrapper.other {
  flex-direction: row;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 8px;
}

.message-content {
  flex: 1;
  max-width: 70%;
  min-width: 0;
}

.sender-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  margin-left: 12px;
}

.message-bubble {
  position: relative;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  cursor: pointer;
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.own-message .message-bubble {
  background: #007bff;
  color: white;
  border-bottom-right-radius: 4px;
  margin-left: auto;
}

.other-message .message-bubble {
  background: #f1f3f5;
  color: #333;
  border-bottom-left-radius: 4px;
  margin-right: auto;
}

.failed-message {
  background: #fee !important;
  border: 1px solid #fcc !important;
}

.message-text {
  line-height: 1.4;
}

.message-image img {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.message-file,
.message-audio {
  margin: 4px 0;
}

.message-attachments {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 11px;
  opacity: 0.7;
}

.message-time {
  font-size: 11px;
}

.message-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  font-size: 12px;
}

.retry-button {
  padding: 0;
  min-height: auto;
  font-size: 12px;
}

.own-message .message-meta {
  color: rgba(255, 255, 255, 0.8);
}

.other-message .message-meta {
  color: #666;
}

/* Responsive */
@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
  }

  .message-bubble {
    padding: 10px 14px;
  }
}
</style>
