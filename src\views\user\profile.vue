<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getUserById } from "./utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { message } from "@/utils/message";

defineOptions({
  name: "UserProfile"
});

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const user = ref<any>(null);

// Computed
const userId = computed(() => {
  return parseInt(route.params.id as string);
});

const pageTitle = computed(() => {
  return user.value ? `${$t("User Profile")} - ${user.value.name}` : $t("User Profile");
});

// Methods
const loadUserProfile = async () => {
  if (!userId.value) return;
  
  loading.value = true;
  try {
    const response = await getUserById(userId.value);
    user.value = useConvertKeyToCamel(response.data);
  } catch (error) {
    console.error("Failed to load user profile:", error);
    message($t("Failed to load user profile"), { type: "error" });
    router.push("/user/management");
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push("/user/management");
};

const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleDateString();
};

const getStatusColor = (status: string) => {
  const colors = {
    active: "success",
    inactive: "info", 
    suspended: "warning",
    banned: "danger",
    pending: "warning"
  };
  return colors[status] || "info";
};

// Lifecycle
onMounted(() => {
  loadUserProfile();
});
</script>

<template>
  <div class="main">
    <!-- Header -->
    <div class="profile-header">
      <div class="flex items-center space-x-4 mb-6">
        <el-button
          type="text"
          size="large"
          @click="goBack"
          class="back-button"
        >
          <IconifyIconOffline 
            :icon="useRenderIcon('ri/arrow-left-line')"
            class="text-xl"
          />
        </el-button>
        
        <div>
          <h1 class="text-2xl font-bold">{{ pageTitle }}</h1>
          <div class="text-sm text-gray-500">
            {{ $t("View user details and information") }}
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <div class="text-center py-12">
        <el-icon class="is-loading text-3xl mb-4">
          <IconifyIconOffline :icon="useRenderIcon('ri/loader-4-line')" />
        </el-icon>
        <p class="text-gray-500">{{ $t("Loading user profile...") }}</p>
      </div>
    </div>

    <!-- Profile Content -->
    <div v-else-if="user" class="profile-content">
      <el-row :gutter="24">
        <!-- User Info Card -->
        <el-col :span="8">
          <el-card class="user-info-card">
            <div class="text-center">
              <el-avatar
                :size="120"
                :src="user.avatar"
                class="mb-4"
              >
                {{ user.name?.charAt(0).toUpperCase() || "U" }}
              </el-avatar>
              
              <h2 class="text-xl font-semibold mb-2">{{ user.name }}</h2>
              <p class="text-gray-500 mb-4">{{ user.email }}</p>
              
              <el-tag
                :type="getStatusColor(user.status)"
                size="large"
                class="mb-4"
              >
                {{ $t(user.status) }}
              </el-tag>
              
              <div class="user-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ user.roles?.length || 0 }}</div>
                  <div class="stat-label">{{ $t("Roles") }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ user.permissions?.length || 0 }}</div>
                  <div class="stat-label">{{ $t("Permissions") }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- Details Card -->
        <el-col :span="16">
          <el-card class="details-card">
            <template #header>
              <div class="flex items-center">
                <IconifyIconOffline 
                  :icon="useRenderIcon('ri/user-line')"
                  class="mr-2"
                />
                {{ $t("User Details") }}
              </div>
            </template>

            <el-descriptions :column="2" border>
              <el-descriptions-item :label="$t('ID')">
                {{ user.id }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('UUID')">
                {{ user.uuid }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Name')">
                {{ user.name }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Email')">
                {{ user.email }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Phone')">
                {{ user.phone || "-" }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Status')">
                <el-tag :type="getStatusColor(user.status)">
                  {{ $t(user.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Email Verified')">
                <el-tag :type="user.emailVerifiedAt ? 'success' : 'warning'">
                  {{ user.emailVerifiedAt ? $t("Verified") : $t("Not Verified") }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Last Login')">
                {{ formatDate(user.lastLoginAt) }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Created At')">
                {{ formatDate(user.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('Updated At')">
                {{ formatDate(user.updatedAt) }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- Bio Section -->
            <div v-if="user.bio" class="mt-6">
              <h3 class="text-lg font-semibold mb-3">{{ $t("Bio") }}</h3>
              <p class="text-gray-700">{{ user.bio }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- Roles and Permissions -->
      <el-row :gutter="24" class="mt-6">
        <!-- Roles Card -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="flex items-center">
                <IconifyIconOffline 
                  :icon="useRenderIcon('ri/shield-user-line')"
                  class="mr-2"
                />
                {{ $t("Roles") }}
              </div>
            </template>

            <div v-if="user.roles && user.roles.length > 0">
              <el-tag
                v-for="role in user.roles"
                :key="role.id"
                class="mr-2 mb-2"
                type="primary"
              >
                {{ role.name }}
              </el-tag>
            </div>
            <div v-else class="text-gray-500">
              {{ $t("No roles assigned") }}
            </div>
          </el-card>
        </el-col>

        <!-- Permissions Card -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="flex items-center">
                <IconifyIconOffline 
                  :icon="useRenderIcon('ri/key-line')"
                  class="mr-2"
                />
                {{ $t("Permissions") }}
              </div>
            </template>

            <div v-if="user.permissions && user.permissions.length > 0">
              <el-tag
                v-for="permission in user.permissions"
                :key="permission.id"
                class="mr-2 mb-2"
                type="success"
                size="small"
              >
                {{ permission.name }}
              </el-tag>
            </div>
            <div v-else class="text-gray-500">
              {{ $t("No permissions assigned") }}
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Error State -->
    <div v-else class="error-state">
      <div class="text-center py-12">
        <IconifyIconOffline 
          :icon="useRenderIcon('ri/error-warning-line')"
          class="text-4xl text-red-500 mb-4"
        />
        <p class="text-gray-500">{{ $t("User not found") }}</p>
        <el-button type="primary" @click="goBack" class="mt-4">
          {{ $t("Go Back") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.main {
  margin: 0;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
}

.profile-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.back-button {
  padding: 8px;
  border-radius: 50%;
  background: #f5f7fa;
}

.back-button:hover {
  background: #e4e7ed;
}

.user-info-card {
  text-align: center;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.details-card :deep(.el-card__header) {
  background: #f5f7fa;
}

.loading-state,
.error-state {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
