import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const userRules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter the user name"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    }
  ],
  email: [
    {
      required: true,
      message: $t("Please enter email address"),
      trigger: "blur"
    },
    {
      type: "email",
      message: $t("Please enter a valid email address"),
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: $t("Please enter password"),
      trigger: "blur"
    },
    {
      min: 8,
      message: $t("Password must be at least 8 characters"),
      trigger: "blur"
    }
  ],
  passwordConfirmation: [
    {
      required: true,
      message: $t("Please confirm password"),
      trigger: "blur"
    }
  ],
  status: [
    {
      required: true,
      message: $t("Please select status"),
      trigger: "change"
    }
  ]
});

export { userRules };
