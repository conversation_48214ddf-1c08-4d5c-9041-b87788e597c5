<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useMessageHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import TableOperations from "@/components/TableOperations.vue";
import PureTable from "@pureadmin/table";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { hasAuth } from "@/router/utils";

// Lazy load components
const MessageDrawerForm = defineAsyncComponent(
  () => import("./components/MessageDrawerForm.vue")
);

const MessageFilterForm = defineAsyncComponent(
  () => import("./components/MessageFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetMessages,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  messageFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore
} = useMessageHook();

onMounted(() => {
  nextTick(() => {
    fnGetMessages();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Message Management')"
        :columns="columns"
        @refresh="fnGetMessages"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('message.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <template #icon>
                <IconifyIconOffline
                  :icon="useRenderIcon('ep:plus')"
                  class="text-[18px]"
                />
              </template>
              {{ $t("Create") }}
            </el-button>
          </el-tooltip>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <el-button
                        class="reset-margin"
                        link
                        type="primary"
                        size="small"
                        :disabled="!hasAuth('message.read')"
                        @click="
                          () => {
                            drawerValues = clone(row, true);
                            drawerVisible = true;
                          }
                        "
                      >
                        {{ $t("View") }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        class="reset-margin"
                        link
                        type="primary"
                        size="small"
                        :disabled="!hasAuth('message.update')"
                        @click="
                          () => {
                            drawerValues = clone(row, true);
                            drawerVisible = true;
                          }
                        "
                      >
                        {{ $t("Edit") }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="!row.deletedAt">
                      <el-button
                        class="reset-margin"
                        link
                        type="danger"
                        size="small"
                        :disabled="!hasAuth('message.destroy')"
                        @click="handleDelete(row)"
                      >
                        {{ $t("Delete") }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="row.deletedAt">
                      <el-button
                        class="reset-margin"
                        link
                        type="success"
                        size="small"
                        :disabled="!hasAuth('message.restore')"
                        @click="handleRestore(row)"
                      >
                        {{ $t("Restore") }}
                      </el-button>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="row.deletedAt">
                      <el-button
                        class="reset-margin"
                        link
                        type="danger"
                        size="small"
                        :disabled="!hasAuth('message.force-delete')"
                        @click="handleDestroy(row)"
                      >
                        {{ $t("Force Delete") }}
                      </el-button>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <!-- Table Operations -->
    <TableOperations
      :multipleSelection="multipleSelection"
      @bulk-delete="handleBulkDelete"
      @bulk-destroy="handleBulkDestroy"
      @bulk-restore="handleBulkRestore"
      :permissions="{
        delete: 'message.destroy',
        forceDelete: 'message.force-delete',
        restore: 'message.restore'
      }"
    />

    <!-- Message Form Drawer -->
    <MessageDrawerForm
      ref="messageFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      :isEdit="!!drawerValues.id"
      @submit="handleSubmit"
    />

    <!-- Filter Form Drawer -->
    <MessageFilterForm
      ref="filterRef"
      v-model:visible="filterVisible"
      v-model:values="{}"
      @submit="handleFilter"
      @reset="
        () => {
          filterVisible = false;
          fnGetMessages();
        }
      "
    />
  </div>
</template>

<style scoped lang="scss">
.main {
  margin: 0;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
}

:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

.reset-margin {
  margin: 0;
}
</style>
