import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";

// Knowledge Base Management APIs

// 4.1 Get knowledge bases list
export const getKnowledgeBases = (params?: {
  per_page?: number;
  status?: string;
  type?: string;
  search?: string;
  page?: number;
}) => {
  return http.request<Result>("get", "/api/v1/auth/knowledge-bases", {
    params
  });
};

// 4.2 Create knowledge base from text
export const createKnowledgeBaseFromText = (data: {
  name: string;
  content: string;
}) => {
  return http.request<Result>("post", "/api/v1/auth/knowledge-bases/text", {
    data: useConvertKeyToSnake(data)
  });
};

// 4.3 Create knowledge base from file
export const createKnowledgeBaseFromFile = (data: {
  name: string;
  file: File;
}) => {
  const formData = new FormData();
  formData.append("name", data.name);
  formData.append("file", data.file);

  return http.request<Result>("post", "/api/v1/auth/knowledge-bases/file", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

// 4.4 Get knowledge base by UUID
export const getKnowledgeBaseById = (uuid: string) => {
  return http.request<Result>("get", `/api/v1/auth/knowledge-bases/${uuid}`);
};

// 4.5 Update knowledge base
export const updateKnowledgeBase = (
  uuid: string,
  data: {
    name?: string;
    content?: string;
  }
) => {
  return http.request<Result>("put", `/api/v1/auth/knowledge-bases/${uuid}`, {
    data: useConvertKeyToSnake(data)
  });
};

// 4.6 Delete knowledge base
export const deleteKnowledgeBase = (uuid: string) => {
  return http.request<Result>("delete", `/api/v1/auth/knowledge-bases/${uuid}`);
};

// Knowledge Base Types
export interface KnowledgeBase {
  uuid: string;
  name: string;
  type: "text" | "file";
  status: "pending" | "processing" | "ready" | "error";
  content?: string;
  content_length?: number;
  file_name?: string;
  file_size?: number;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface KnowledgeBaseListResponse {
  success: boolean;
  message: string;
  data: KnowledgeBase[];
  meta: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
  };
}

export interface KnowledgeBaseResponse {
  success: boolean;
  message: string;
  data: KnowledgeBase;
}
