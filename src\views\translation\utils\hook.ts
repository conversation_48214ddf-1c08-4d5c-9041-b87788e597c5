import { reactive, ref } from "vue";
import {
  getTranslations,
  createTranslation,
  updateTranslationById,
  deleteTranslationById,
  bulkDeleteTranslations,
  bulkDestroyTranslations,
  destroyTranslationById,
  restoreTranslationById,
  bulkRestoreTranslations,
  getTranslationGroups,
  syncTranslations,
  exportTranslations,
  importTranslations
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { TranslationFilterProps } from "@/views/translation/utils/type";

export function useTranslationHook() {
  // Reactive state
  const loading = ref(false);
  const syncLoading = ref(false);
  const exportLoading = ref(false);
  const importLoading = ref(false);
  const filterRef = ref<TranslationFilterProps>({});
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100]
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const groups = ref([]);
  const sort = ref({ sortBy: "key", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const importVisible = ref(false);
  const drawerValues = ref<FieldValues>({ status: "active", isActive: true });
  const translationFormRef = ref();

  // API Handlers
  const fnGetTranslations = async () => {
    loading.value = true;
    try {
      const response = await getTranslations({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  const fnGetGroups = async () => {
    try {
      const response = await getTranslationGroups();
      groups.value = useConvertKeyToCamel(response.data);
    } catch (e) {
      console.error("Failed to load groups:", e);
    }
  };

  // Event handlers
  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetTranslations();
  };

  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetTranslations();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetTranslations();
  };

  // Form related
  const handleSubmit = async (values: FieldValues) => {
    try {
      if (values.id) {
        await updateTranslationById(values.id, values);
        message($t("Update successful"), { type: "success" });
      } else {
        await createTranslation(values);
        message($t("Create successful"), { type: "success" });
      }
      drawerVisible.value = false;
      fnGetTranslations();
    } catch (e) {
      message(e.response?.data?.message || e?.message || $t("Operation failed"), {
        type: "error"
      });
    }
  };

  const handleFilter = () => {
    pagination.currentPage = 1;
    fnGetTranslations();
    filterVisible.value = false;
  };

  // Delete operations
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this translation?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await deleteTranslationById(row.id);
      message($t("Delete successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(error.response?.data?.message || error?.message || $t("Delete failed"), {
          type: "error"
        });
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected translations?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await bulkDeleteTranslations({ ids });
      message($t("Delete successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(error.response?.data?.message || error?.message || $t("Delete failed"), {
          type: "error"
        });
      }
    }
  };

  // Destroy operations
  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this translation?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await destroyTranslationById(row.id);
      message($t("Destroy successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(error.response?.data?.message || error?.message || $t("Destroy failed"), {
          type: "error"
        });
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to destroy"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected translations?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await bulkDestroyTranslations({ ids });
      message($t("Destroy successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(error.response?.data?.message || error?.message || $t("Destroy failed"), {
          type: "error"
        });
      }
    }
  };

  // Restore operations
  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this translation?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      await restoreTranslationById(row.id);
      message($t("Restore successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(error.response?.data?.message || error?.message || $t("Restore failed"), {
          type: "error"
        });
      }
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected translations?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await bulkRestoreTranslations({ ids });
      message($t("Restore successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(error.response?.data?.message || error?.message || $t("Restore failed"), {
          type: "error"
        });
      }
    }
  };

  // Special operations
  const handleSync = async (languageCode: string) => {
    syncLoading.value = true;
    try {
      await syncTranslations({ languageCode });
      message($t("Sync successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      message(error.response?.data?.message || error?.message || $t("Sync failed"), {
        type: "error"
      });
    } finally {
      syncLoading.value = false;
    }
  };

  const handleExport = async (params?: object) => {
    exportLoading.value = true;
    try {
      const response = await exportTranslations(params);
      // Handle file download
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "translations.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      message($t("Export successful"), { type: "success" });
    } catch (error) {
      message(error.response?.data?.message || error?.message || $t("Export failed"), {
        type: "error"
      });
    } finally {
      exportLoading.value = false;
    }
  };

  const handleImport = async (file: File) => {
    importLoading.value = true;
    try {
      const formData = new FormData();
      formData.append("file", file);
      await importTranslations(formData);
      message($t("Import successful"), { type: "success" });
      importVisible.value = false;
      fnGetTranslations();
    } catch (error) {
      message(error.response?.data?.message || error?.message || $t("Import failed"), {
        type: "error"
      });
    } finally {
      importLoading.value = false;
    }
  };

  return {
    // Data/State
    loading,
    syncLoading,
    exportLoading,
    importLoading,
    filterRef,
    pagination,
    records,
    groups,
    multipleSelection,
    // Event handlers
    handleBulkDelete,
    handleDelete,
    fnGetTranslations,
    fnGetGroups,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    // Form related
    filterVisible,
    drawerVisible,
    importVisible,
    drawerValues,
    translationFormRef,
    handleSubmit,
    handleFilter,
    handleBulkDestroy,
    handleBulkRestore,
    handleDestroy,
    handleRestore,
    // Special operations
    handleSync,
    handleExport,
    handleImport
  };
}
