<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useBotStoreHook } from "@/store/modules/bot";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { $t } from "@/plugins/i18n";
import BotCard from "@/views/bot/components/BotCard.vue";

const router = useRouter();
const botStore = useBotStoreHook();
const bots = ref([]);
// Methods
const createNewBot = () => {
  router.push("/bots/agent");
};

onMounted(async () => {
  await botStore.fetchBots();
  bots.value = botStore.bots;
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <header
        class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6"
      >
        <div>
          <h1 class="text-2xl font-bold text-gray-800">Bot Agent</h1>
          <p class="text-gray-500">Quản lý bots của bạn.</p>
        </div>
        <el-button
          type="primary"
          round
          size="large"
          class="mt-4 sm:mt-0"
          @click="createNewBot"
        >
          <IconifyIconOnline :icon="'tabler:plus'" class="mr-2" />
          Tạo AI Agent
        </el-button>
      </header>
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div
          class="w-full h-full min-h-[356px] rounded-2xl border-2 border-dashed border-gray-300 flex flex-col items-center justify-center text-gray-500 hover:border-indigo-500 hover:text-indigo-500 transition-colors duration-300 cursor-pointer bg-white"
          @click="createNewBot"
        >
          <IconifyIconOnline icon="line-md:plus" class="text-8xl" />
          <span class="font-semibold text-lg"> Tạo AI Agent </span>
        </div>
        <BotCard v-for="bot in bots" :key="bot.uuid" :bot="bot" />
      </div>
    </div>
  </div>
</template>
