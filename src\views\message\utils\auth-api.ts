import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/message/utils/type";

export const getMessages = (params?: object) => {
  return http.request<Result>("get", "/api/auth/messages", {
    params
  });
};

export const getMessageById = (id: number) => {
  return http.request<Result>("get", `/api/auth/messages/${id}`);
};

export const createMessage = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/messages", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateMessageById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/messages/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteMessageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/messages/${id}`);
};

export const bulkDeleteMessages = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/messages/bulk", {
    data: { ids }
  });
};

export const destroyMessageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/messages/${id}/force`);
};

export const bulkDestroyMessages = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/messages/bulk/force", {
    data: { ids }
  });
};

export const restoreMessageById = (id: number) => {
  return http.request<Result>("post", `/api/auth/messages/${id}/restore`);
};

export const bulkRestoreMessages = (ids: number[]) => {
  return http.request<Result>("post", "/api/auth/messages/bulk/restore", {
    data: { ids }
  });
};

// Get Chats for dropdown
export const getChats = () => {
  return http.request<Result>("get", "/api/auth/chats");
};

// Get Messages by Chat ID
export const getMessagesByChatId = (chatId: number, params?: object) => {
  return http.request<Result>("get", `/api/auth/chats/${chatId}/messages`, {
    params
  });
};
