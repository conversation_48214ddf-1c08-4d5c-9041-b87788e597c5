const Layout = () => import("@/layout/index.vue");

export default {
  path: "/user",
  name: "User",
  component: Layout,
  redirect: "/user/management",
  meta: {
    icon: "ri/user-line",
    title: "User Management",
    rank: 7
  },
  children: [
    {
      path: "/user/management",
      name: "UserManagement",
      component: () => import("@/views/user/index.vue"),
      meta: {
        title: "User Management",
        showLink: true,
        auths: ["user.read"]
      }
    },
    {
      path: "/user/profile/:id",
      name: "UserProfile",
      component: () => import("@/views/user/profile.vue"),
      meta: {
        title: "User Profile",
        showLink: false,
        auths: ["user.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
