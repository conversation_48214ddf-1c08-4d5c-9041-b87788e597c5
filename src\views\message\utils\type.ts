export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  chatId?: number;
  role?: 'user' | 'assistant' | 'system';
  content?: string;
  contentType?: 'text' | 'image' | 'file' | 'audio';
  attachments?: any[];
  metadata?: any;
  parentMessageId?: number;
  isEdited?: boolean;
  editedAt?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  // Relationship data
  chat?: {
    id: number;
    title: string;
    user?: {
      id: number;
      name: string;
      email: string;
    };
    bot?: {
      id: number;
      name: string;
    };
  };
  parentMessage?: {
    id: number;
    content: string;
    role: string;
  };
  replies?: FormItemProps[];
};

export type MessageFilterProps = {
  chatId?: number;
  role?: 'user' | 'assistant' | 'system';
  contentType?: 'text' | 'image' | 'file' | 'audio';
  content?: string;
  dateFrom?: string;
  dateTo?: string;
  isEdited?: boolean;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
