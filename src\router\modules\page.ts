const Layout = () => import("@/layout/index.vue");

export default {
  path: "/page",
  name: "Page",
  component: Layout,
  redirect: "/page/index",
  meta: {
    icon: "ri/file-text-line",
    title: "Page Management",
    rank: 5
  },
  children: [
    {
      path: "/page/index",
      name: "PageIndex",
      component: () => import("@/views/page/index.vue"),
      meta: {
        title: "Page Management",
        showLink: true,
        auths: ["page:list"]
      }
    }
  ]
} satisfies RouteConfigsTable;
