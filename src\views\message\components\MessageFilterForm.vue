<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, onMounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getChats } from "../utils/auth-api";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();
const chats = ref([]);

onMounted(async () => {
  await loadChats();
});

const loadChats = async () => {
  try {
    const { data } = await getChats();
    if (data?.success) {
      chats.value = data.data.map((chat: any) => ({
        label: chat.title || `Chat #${chat.id}`,
        value: chat.id
      }));
    }
  } catch (error) {
    console.error("Error loading chats:", error);
  }
};

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Chat")),
    prop: "chatId",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by chat"),
      clearable: true,
      filterable: true
    },
    options: computed(() => chats.value)
  },
  {
    label: computed(() => $t("Role")),
    prop: "role",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by role"),
      clearable: true
    },
    options: [
      { label: $t("User"), value: "user" },
      { label: $t("Assistant"), value: "assistant" },
      { label: $t("System"), value: "system" }
    ]
  },
  {
    label: computed(() => $t("Content Type")),
    prop: "contentType",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by content type"),
      clearable: true
    },
    options: [
      { label: $t("Text"), value: "text" },
      { label: $t("Image"), value: "image" },
      { label: $t("File"), value: "file" },
      { label: $t("Audio"), value: "audio" }
    ]
  },
  {
    label: computed(() => $t("Content")),
    prop: "content",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Search by content"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Date From")),
    prop: "dateFrom",
    valueType: "date-picker",
    fieldProps: {
      placeholder: $t("Select start date"),
      clearable: true,
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD"
    }
  },
  {
    label: computed(() => $t("Date To")),
    prop: "dateTo",
    valueType: "date-picker",
    fieldProps: {
      placeholder: $t("Select end date"),
      clearable: true,
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD"
    }
  },
  {
    label: computed(() => $t("Is Edited")),
    prop: "isEdited",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by edit status"),
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ]
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by trash status"),
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: "yes" },
      { label: $t("No"), value: "no" }
    ]
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
};

const handleReset = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
  emit("reset");
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  width: 100%;
}
</style>
