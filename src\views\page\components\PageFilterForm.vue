<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, onMounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const statusOptions = [
  { label: $t("All"), value: "" },
  { label: $t("Draft"), value: "draft" },
  { label: $t("Published"), value: "published" },
  { label: $t("Archived"), value: "archived" }
];

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Locale")),
    prop: "locale",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter locale"),
      clearable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Title")),
    prop: "title",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter title"),
      clearable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Slug")),
    prop: "slug",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter slug"),
      clearable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select status"),
      clearable: true
    },
    options: statusOptions,
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Author ID")),
    prop: "authorId",
    valueType: "input-number",
    fieldProps: {
      placeholder: $t("Enter author ID"),
      clearable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Trash Status")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select trash status"),
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "no" },
      { label: $t("Trashed"), value: "yes" }
    ],
    colProps: { span: 12 }
  }
];

const handleSubmit = async () => {
  try {
    loading.value = true;
    emit("submit", props.values);
    emit("update:visible", false);
  } catch (error) {
    console.error("Filter submission failed:", error);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  emit("reset");
  emit("update:visible", false);
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter Pages") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ $t("Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
