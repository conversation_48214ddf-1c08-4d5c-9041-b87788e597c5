// Mock API interceptor for development
import axios from "axios";
import {
  mockUsers,
  mockAiModels,
  mockBots,
  mockChats,
  mockMessages,
  mockDashboardStats,
  mockRecentActivities,
  paginate,
  delay
} from "./mock-data";

// Enable mock mode (set to false to use real API)
export const MOCK_MODE = import.meta.env.VITE_MOCK_API === "true";

// Mock API responses
const mockResponses = {
  // Dashboard endpoints
  "GET /api/auth/dashboard/stats": () => ({
    success: true,
    data: mockDashboardStats
  }),

  "GET /api/auth/dashboard/recent-activities": (params: any) => {
    const { limit = 10 } = params;
    const activities = mockRecentActivities.slice(0, limit);
    return {
      success: true,
      data: activities
    };
  },

  // Bot endpoints
  "GET /api/auth/bots": (params: any) => {
    const { page = 1, per_page = 10, sort_by = "id", sort_order = "desc" } = params;
    let data = [...mockBots];
    
    // Apply sorting
    data.sort((a, b) => {
      const aVal = a[sort_by as keyof typeof a];
      const bVal = b[sort_by as keyof typeof b];
      const order = sort_order === "asc" ? 1 : -1;
      return aVal > bVal ? order : -order;
    });

    const result = paginate(data, page, per_page);
    return {
      success: true,
      data: result
    };
  },

  "GET /api/auth/bots/:id": (params: any, pathParams: any) => {
    const bot = mockBots.find(b => b.id === parseInt(pathParams.id));
    return {
      success: !!bot,
      data: bot || null
    };
  },

  "POST /api/auth/bots": (data: any) => {
    const newBot = {
      id: Math.max(...mockBots.map(b => b.id)) + 1,
      uuid: `bot-${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockBots.push(newBot);
    return {
      success: true,
      data: newBot
    };
  },

  "PUT /api/auth/bots/:id": (data: any, pathParams: any) => {
    const index = mockBots.findIndex(b => b.id === parseInt(pathParams.id));
    if (index !== -1) {
      mockBots[index] = {
        ...mockBots[index],
        ...data,
        updatedAt: new Date().toISOString()
      };
      return {
        success: true,
        data: mockBots[index]
      };
    }
    return { success: false, message: "Bot not found" };
  },

  "DELETE /api/auth/bots/:id": (data: any, pathParams: any) => {
    const index = mockBots.findIndex(b => b.id === parseInt(pathParams.id));
    if (index !== -1) {
      mockBots[index].deletedAt = new Date().toISOString();
      return { success: true };
    }
    return { success: false, message: "Bot not found" };
  },

  // Chat endpoints
  "GET /api/auth/chat/bots": () => {
    // Trả về bots với conversation có sẵn
    return {
      success: true,
      data: mockBots
    };
  },

  "GET /api/auth/chats": (params: any) => {
    const { page = 1, per_page = 10 } = params;
    const result = paginate(mockChats, page, per_page);
    return {
      success: true,
      data: result
    };
  },

  "POST /api/auth/chats": (data: any) => {
    const newChat = {
      id: Math.max(...mockChats.map(c => c.id)) + 1,
      uuid: `chat-${Date.now()}`,
      ...data,
      messageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockChats.push(newChat);
    return {
      success: true,
      data: newChat
    };
  },

  // Message endpoints
  "GET /api/auth/messages": (params: any) => {
    const { page = 1, per_page = 20 } = params;
    const result = paginate(mockMessages, page, per_page);
    return {
      success: true,
      data: result
    };
  },

  "POST /api/auth/messages": (data: any) => {
    const newMessage = {
      id: Math.max(...mockMessages.map(m => m.id)) + 1,
      uuid: `msg-${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockMessages.push(newMessage);
    return {
      success: true,
      data: newMessage
    };
  },

  // User endpoints
  "GET /api/auth/users": () => ({
    success: true,
    data: mockUsers
  }),

  // AI Model endpoints
  "GET /api/auth/model-ai": () => ({
    success: true,
    data: mockAiModels
  })
};

// Setup mock interceptor
export const setupMockApi = () => {
  if (!MOCK_MODE) return;

  console.log("🔧 Mock API enabled");

  // Request interceptor
  axios.interceptors.request.use(async (config) => {
    const method = config.method?.toUpperCase();
    const url = config.url;
    
    if (!method || !url) return config;

    // Extract path parameters
    const pathParams: Record<string, string> = {};
    let normalizedUrl = url;
    
    // Replace path parameters with placeholders
    normalizedUrl = normalizedUrl.replace(/\/(\d+)/g, (match, id) => {
      pathParams.id = id;
      return "/:id";
    });

    const mockKey = `${method} ${normalizedUrl}`;
    const mockHandler = mockResponses[mockKey as keyof typeof mockResponses];

    if (mockHandler) {
      // Simulate network delay
      await delay(300);

      const params = method === "GET" ? config.params : config.data;
      const response = mockHandler(params, pathParams);

      // Create mock response
      const mockResponse = {
        data: response,
        status: response.success ? 200 : 400,
        statusText: response.success ? "OK" : "Bad Request",
        headers: {},
        config
      };

      // Cancel the real request and return mock response
      const cancelToken = axios.CancelToken.source();
      config.cancelToken = cancelToken.token;
      
      // Schedule the mock response
      setTimeout(() => {
        if (response.success) {
          config.adapter = () => Promise.resolve(mockResponse);
        } else {
          config.adapter = () => Promise.reject({
            response: mockResponse,
            message: response.message || "Mock API Error"
          });
        }
      }, 0);
    }

    return config;
  });

  // Response interceptor for additional mock handling
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.message?.includes("Mock API")) {
        console.warn("Mock API Error:", error.message);
      }
      return Promise.reject(error);
    }
  );
};

// Initialize mock API if enabled
if (MOCK_MODE) {
  setupMockApi();
}
