<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import dayjs from "dayjs";

const props = defineProps({
  bot: {
    type: Object,
    required: true
  }
});

const formatTime = (datetime: any) => {
  return dayjs(datetime).format("DD/MM/YYYY");
};
</script>

<template>
  <div class="group">
    <el-card
      class="transform transition-all !rounded-[12px] duration-300 hover:scale-105 hover:shadow-2xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden"
    >
      <template #header>
        <div
          class="bg-gradient-to-r from-emerald-500 to-teal-500 -m-5 mb-1 p-4 text-white"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div
                class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm"
              >
                <el-avatar
                  fit="cover"
                  :src="bot.logo"
                  :icon="useRenderIcon('line-md:robot')"
                />
              </div>
              <div>
                <h3
                  class="text-sm font-bold leading-6 line-clamp-1"
                  :title="bot.name"
                >
                  {{ bot.name }}
                </h3>
                <p class="text-emerald-100 text-sm">
                  {{ bot.aiModel?.name }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <i class="el-icon-user text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">Người tạo</p>
              <p class="font-semibold text-gray-800 text-sm">
                {{ bot.owner?.fullName }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <i class="el-icon-calendar text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">Ngày tạo</p>
              <p class="font-semibold text-gray-800 text-sm">
                {{ formatTime(bot.createdAt) }}
              </p>
            </div>
          </div>
        </div>

        <div>
          <p class="text-sm text-gray-600 leading-6 line-clamp-3 h-[75px]">
            {{ bot.description }}
          </p>
        </div>

        <div class="flex space-x-2 pt-2">
          <el-button
            round
            type="success"
            size="small"
            class="flex-1 !uppercase"
          >
            <i class="el-icon-picture-outline mr-1" />
            Vào chat
          </el-button>
          <el-button size="small" round type="warning">
            <i class="el-icon-view mr-1" />
            Chi tiết
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss"></style>
