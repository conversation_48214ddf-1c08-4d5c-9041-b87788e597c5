<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();

const statusOptions = [
  { label: $t("Draft"), value: "draft" },
  { label: $t("Published"), value: "published" },
  { label: $t("Archived"), value: "archived" }
];

const isEdit = computed(() => !!props.values?.id);

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Locale")),
    prop: "locale",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter locale (e.g., en, vi)")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Title")),
    prop: "title",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter title")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Slug")),
    prop: "slug",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter slug")
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("GJS Components")),
    prop: "gjsComponents",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter GJS components JSON"),
      autosize: { minRows: 4, maxRows: 8 }
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("GJS Styles")),
    prop: "gjsStyles",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter GJS styles JSON"),
      autosize: { minRows: 4, maxRows: 8 }
    },
    colProps: { span: 24 }
  },

  {
    label: computed(() => $t("GJS HTML")),
    prop: "gjsHtml",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter GJS HTML"),
      autosize: { minRows: 4, maxRows: 8 }
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Meta Title")),
    prop: "metaTitle",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter meta title")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Meta Description")),
    prop: "metaDescription",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter meta description"),
      autosize: { minRows: 2, maxRows: 4 }
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Meta Keywords")),
    prop: "metaKeywords",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter meta keywords")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Select status")
    },
    options: statusOptions,
    colProps: { span: 12 }
  }
];
];

const handleSubmit = async () => {
  try {
    loading.value = true;
    emit("submit", props.values);
    emit("update:visible", false);
  } catch (error) {
    console.error("Form submission failed:", error);
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: true,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ isEdit ? $t("Edit Page") : $t("Add Page") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ $t("Submit") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
