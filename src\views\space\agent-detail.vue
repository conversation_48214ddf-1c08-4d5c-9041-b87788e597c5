<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const slug = ref(route.params.slug);
const agentId = ref(route.params.agentId);

onMounted(() => {
  console.log("Agent Detail Page - Slug:", slug.value, "Agent ID:", agentId.value);
});
</script>

<template>
  <div class="main">
    <div class="p-4">
      <h1 class="text-2xl font-bold text-gray-800 mb-4">Agent Detail</h1>
      <div class="bg-white rounded-lg shadow p-6">
        <p class="text-gray-600">Space: {{ slug }}</p>
        <p class="text-gray-600">Agent ID: {{ agentId }}</p>
        <p class="text-gray-500 mt-4">This is the agent detail page.</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply min-h-screen bg-gray-50;
}
</style>
