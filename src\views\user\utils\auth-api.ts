import { http } from "@/utils/http";
import type { FieldValues } from "plus-pro-components";

// Get users with pagination and filters
export const getUsers = (params?: FieldValues) => {
  return http.request<any>("get", "/auth/users", { params });
};

// Get single user by ID
export const getUserById = (id: number) => {
  return http.request<any>("get", `/auth/users/${id}`);
};

// Create new user
export const createUser = (data: FieldValues) => {
  return http.request<any>("post", "/auth/users", { data });
};

// Update user by ID
export const updateUserById = (id: number, data: FieldValues) => {
  return http.request<any>("put", `/auth/users/${id}`, { data });
};

// Soft delete user by ID
export const deleteUserById = (id: number) => {
  return http.request<any>("delete", `/auth/users/${id}`);
};

// Permanently delete user by ID
export const destroyUserById = (id: number) => {
  return http.request<any>("delete", `/auth/users/${id}/force`);
};

// Restore soft deleted user by ID
export const restoreUserById = (id: number) => {
  return http.request<any>("post", `/auth/users/${id}/restore`);
};

// Bulk soft delete users
export const bulkDeleteUsers = (ids: number[]) => {
  return http.request<any>("delete", "/auth/users/bulk", { data: { ids } });
};

// Bulk permanently delete users
export const bulkDestroyUsers = (ids: number[]) => {
  return http.request<any>("delete", "/auth/users/bulk/force", { data: { ids } });
};

// Bulk restore users
export const bulkRestoreUsers = (ids: number[]) => {
  return http.request<any>("post", "/auth/users/bulk/restore", { data: { ids } });
};

// Update user status
export const updateUserStatus = (id: number, status: string) => {
  return http.request<any>("patch", `/auth/users/${id}/status`, { 
    data: { status } 
  });
};

// Update user password
export const updateUserPassword = (id: number, data: { password: string; password_confirmation: string }) => {
  return http.request<any>("patch", `/auth/users/${id}/password`, { data });
};

// Update user avatar
export const updateUserAvatar = (id: number, avatar: string) => {
  return http.request<any>("patch", `/auth/users/${id}/avatar`, { 
    data: { avatar } 
  });
};

// Get user roles
export const getUserRoles = (id: number) => {
  return http.request<any>("get", `/auth/users/${id}/roles`);
};

// Update user roles
export const updateUserRoles = (id: number, roleIds: number[]) => {
  return http.request<any>("put", `/auth/users/${id}/roles`, { 
    data: { role_ids: roleIds } 
  });
};

// Get user permissions
export const getUserPermissions = (id: number) => {
  return http.request<any>("get", `/auth/users/${id}/permissions`);
};

// Update user permissions
export const updateUserPermissions = (id: number, permissionIds: number[]) => {
  return http.request<any>("put", `/auth/users/${id}/permissions`, { 
    data: { permission_ids: permissionIds } 
  });
};

// Get all roles for dropdown
export const getRoles = () => {
  return http.request<any>("get", "/auth/roles");
};

// Get all permissions for dropdown
export const getPermissions = () => {
  return http.request<any>("get", "/auth/permissions");
};

// Send password reset email
export const sendPasswordReset = (id: number) => {
  return http.request<any>("post", `/auth/users/${id}/password-reset`);
};

// Send email verification
export const sendEmailVerification = (id: number) => {
  return http.request<any>("post", `/auth/users/${id}/email-verification`);
};

// Verify user email
export const verifyUserEmail = (id: number) => {
  return http.request<any>("patch", `/auth/users/${id}/verify-email`);
};

// Suspend user account
export const suspendUser = (id: number, reason?: string) => {
  return http.request<any>("patch", `/auth/users/${id}/suspend`, { 
    data: { reason } 
  });
};

// Unsuspend user account
export const unsuspendUser = (id: number) => {
  return http.request<any>("patch", `/auth/users/${id}/unsuspend`);
};

// Ban user account
export const banUser = (id: number, reason?: string) => {
  return http.request<any>("patch", `/auth/users/${id}/ban`, { 
    data: { reason } 
  });
};

// Unban user account
export const unbanUser = (id: number) => {
  return http.request<any>("patch", `/auth/users/${id}/unban`);
};

// Get user activity logs
export const getUserActivityLogs = (id: number, params?: FieldValues) => {
  return http.request<any>("get", `/auth/users/${id}/activity-logs`, { params });
};

// Get user login history
export const getUserLoginHistory = (id: number, params?: FieldValues) => {
  return http.request<any>("get", `/auth/users/${id}/login-history`, { params });
};

// Export users to CSV/Excel
export const exportUsers = (params?: FieldValues) => {
  return http.request<any>("get", "/auth/users/export", { 
    params,
    responseType: "blob"
  });
};

// Import users from CSV/Excel
export const importUsers = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);
  
  return http.request<any>("post", "/auth/users/import", { 
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

// Get user statistics
export const getUserStats = () => {
  return http.request<any>("get", "/auth/users/stats");
};
