#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob
import shutil
from pathlib import Path

# Comprehensive dictionary of Chinese to English translations
translations = {
    # Common UI terms
    "确定": "Confirm",
    "取消": "Cancel",
    "保存": "Save",
    "删除": "Delete",
    "编辑": "Edit",
    "添加": "Add",
    "新增": "Add",
    "修改": "Modify",
    "查看": "View",
    "搜索": "Search",
    "重置": "Reset",
    "提交": "Submit",
    "返回": "Back",
    "关闭": "Close",
    "打开": "Open",
    "刷新": "Refresh",
    "加载": "Load",
    "上传": "Upload",
    "下载": "Download",
    "导入": "Import",
    "导出": "Export",
    "复制": "Copy",
    "粘贴": "Paste",
    "剪切": "Cut",
    "全选": "Select All",
    "清空": "Clear",
    "重新加载": "Reload",
    "复制成功": "Copy successful",
    "复制失败": "Copy failed",

    # Status terms
    "成功": "Success",
    "失败": "Failed",
    "错误": "Error",
    "警告": "Warning",
    "信息": "Info",
    "提示": "Tip",
    "通知": "Notification",
    "消息": "Message",
    "状态": "Status",
    "启用": "Enable",
    "禁用": "Disable",
    "激活": "Active",
    "停用": "Inactive",
    "正常": "Normal",
    "异常": "Abnormal",
    "在线": "Online",
    "离线": "Offline",
    "已连接": "Connected",
    "未连接": "Disconnected",
    "进行中": "In progress",
    "未开始": "Not started",
    "已完成": "Completed",
    "已取消": "Cancelled",
    "马上到期": "About to expire",
    "已耗时": "elapsed",

    # Form terms
    "用户名": "Username",
    "密码": "Password",
    "邮箱": "Email",
    "手机号": "Phone",
    "姓名": "Name",
    "昵称": "Nickname",
    "头像": "Avatar",
    "性别": "Gender",
    "年龄": "Age",
    "生日": "Birthday",
    "地址": "Address",
    "备注": "Remark",
    "描述": "Description",
    "标题": "Title",
    "内容": "Content",
    "类型": "Type",
    "分类": "Category",
    "标签": "Tag",
    "排序": "Sort",
    "权重": "Weight",
    "优先级": "Priority",

    # Time terms
    "时间": "Time",
    "日期": "Date",
    "创建时间": "Created At",
    "更新时间": "Updated At",
    "开始时间": "Start Time",
    "结束时间": "End Time",
    "今天": "Today",
    "昨天": "Yesterday",
    "明天": "Tomorrow",
    "本周": "This Week",
    "本月": "This Month",
    "本年": "This Year",

    # Navigation terms
    "首页": "Home",
    "菜单": "Menu",
    "导航": "Navigation",
    "面包屑": "Breadcrumb",
    "侧边栏": "Sidebar",
    "顶部": "Top",
    "底部": "Bottom",
    "左侧": "Left",
    "右侧": "Right",
    "中间": "Center",
    "全屏": "Fullscreen",
    "退出全屏": "Exit Fullscreen",
    "折叠": "Collapse",
    "展开": "Expand",

    # System terms
    "系统": "System",
    "设置": "Settings",
    "配置": "Configuration",
    "管理": "Management",
    "控制台": "Console",
    "仪表板": "Dashboard",
    "统计": "Statistics",
    "报表": "Report",
    "日志": "Log",
    "监控": "Monitor",
    "安全": "Security",
    "权限": "Permission",
    "角色": "Role",
    "用户": "User",
    "组织": "Organization",
    "部门": "Department",

    # Data terms
    "数据": "Data",
    "列表": "List",
    "表格": "Table",
    "详情": "Details",
    "总计": "Total",
    "数量": "Quantity",
    "金额": "Amount",
    "价格": "Price",
    "单价": "Unit Price",
    "小计": "Subtotal",
    "合计": "Total",
    "页码": "Page",
    "每页": "Per Page",
    "共": "Total",
    "条": "items",
    "页": "pages",

    # Action terms
    "操作": "Actions",
    "功能": "Function",
    "选择": "Select",
    "选中": "Selected",
    "全部": "All",
    "批量": "Batch",
    "单个": "Single",
    "多个": "Multiple",
    "请选择": "Please select",
    "请输入": "Please enter",
    "请上传": "Please upload",
    "请确认": "Please confirm",

    # Validation terms
    "必填": "Required",
    "可选": "Optional",
    "格式错误": "Invalid format",
    "长度不符": "Invalid length",
    "不能为空": "Cannot be empty",
    "已存在": "Already exists",
    "不存在": "Does not exist",
    "超出限制": "Exceeds limit",
    "验证失败": "Validation failed",
    "验证成功": "Validation successful",

    # File terms
    "文件": "File",
    "文件夹": "Folder",
    "图片": "Image",
    "视频": "Video",
    "音频": "Audio",
    "文档": "Document",
    "压缩包": "Archive",
    "大小": "Size",
    "格式": "Format",
    "扩展名": "Extension",

    # Network terms
    "网络": "Network",
    "连接": "Connection",
    "请求": "Request",
    "响应": "Response",
    "超时": "Timeout",
    "重试": "Retry",
    "加载中": "Loading",
    "加载失败": "Load failed",
    "网络错误": "Network error",
    "服务器错误": "Server error",

    # Comments
    "注释": "Comment",
    "说明": "Description",
    "备注": "Note",
    "提示": "Hint",
    "帮助": "Help",
    "文档": "Documentation",
    "示例": "Example",
    "演示": "Demo",
    "测试": "Test",
    "调试": "Debug",
}

def replace_chinese_in_file(file_path):
    """Replace Chinese text in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Replace each Chinese term with its English equivalent
        for chinese, english in translations.items():
            # Replace exact matches (word boundaries)
            content = re.sub(r'\b' + re.escape(chinese) + r'\b', english, content)
            # Also replace in comments and strings
            content = re.sub(r'(["\'].*?)' + re.escape(chinese) + r'(.*?["\'])',
                           r'\1' + english + r'\2', content)
            content = re.sub(r'(//.*?)' + re.escape(chinese) + r'(.*?)$',
                           r'\1' + english + r'\2', content, flags=re.MULTILINE)
            content = re.sub(r'(/\*.*?)' + re.escape(chinese) + r'(.*?\*/)',
                           r'\1' + english + r'\2', content, flags=re.DOTALL)

        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Updated: {file_path}")
            return True
        return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def check_remaining_chinese():
    """Check how many files still contain Chinese characters"""
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')

    files_with_chinese = []
    total_files = 0

    for root, dirs, files in os.walk("src"):
        for file in files:
            if file.endswith(('.vue', '.ts', '.js', '.json')) and not file.endswith('.bak'):
                filepath = os.path.join(root, file)
                total_files += 1
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if chinese_pattern.search(content):
                            files_with_chinese.append(filepath)
                            # Show sample Chinese characters
                            matches = chinese_pattern.findall(content)
                            print(f"Chinese found in {filepath}: {matches[:3]}")
                except Exception as e:
                    print(f"Error reading {filepath}: {e}")

    print(f"\nSummary:")
    print(f"Total files checked: {total_files}")
    print(f"Files with Chinese: {len(files_with_chinese)}")
    print(f"Files successfully processed: {total_files - len(files_with_chinese)}")

    if len(files_with_chinese) == 0:
        print("🎉 SUCCESS: All Chinese characters have been replaced!")
    else:
        print(f"⚠️  Still need to process {len(files_with_chinese)} files")

    return files_with_chinese

def main():
    """Main function to process all files in src directory"""
    src_dir = "src"
    if not os.path.exists(src_dir):
        print("src directory not found!")
        return

    # First check current status
    print("Checking current status...")
    remaining_files = check_remaining_chinese()

    if len(remaining_files) == 0:
        print("No Chinese characters found. Task completed!")
        return

    # Find all relevant files
    file_patterns = [
        "src/**/*.vue",
        "src/**/*.ts",
        "src/**/*.js",
        "src/**/*.json"
    ]

    files_to_process = []
    for pattern in file_patterns:
        files_to_process.extend(glob.glob(pattern, recursive=True))

    # Filter out backup files
    files_to_process = [f for f in files_to_process if not f.endswith('.bak')]

    print(f"Found {len(files_to_process)} files to process")

    updated_count = 0
    for file_path in files_to_process:
        if replace_chinese_in_file(file_path):
            updated_count += 1

    print(f"Updated {updated_count} files")

    # Check again after processing
    print("\nChecking status after processing...")
    check_remaining_chinese()

if __name__ == "__main__":
    main()