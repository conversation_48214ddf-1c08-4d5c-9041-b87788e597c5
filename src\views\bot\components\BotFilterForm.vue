<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, onMounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getAiModels } from "../utils/auth-api";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();
const aiModels = ref([]);

onMounted(async () => {
  await loadAiModels();
});

const loadAiModels = async () => {
  try {
    const { data } = await getAiModels();
    if (data?.success) {
      aiModels.value = data.data.map((model: any) => ({
        label: model.name,
        value: model.id
      }));
    }
  } catch (error) {
    console.error("Error loading AI models:", error);
  }
};

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Bot Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Search by bot name"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("AI Model")),
    prop: "aiModelId",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by AI model"),
      clearable: true
    },
    options: computed(() => aiModels.value)
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by status"),
      clearable: true
    },
    options: [
      { label: $t("Draft"), value: "draft" },
      { label: $t("Review"), value: "review" },
      { label: $t("Active"), value: "active" },
      { label: $t("Paused"), value: "paused" },
      { label: $t("Banned"), value: "banned" }
    ]
  },
  {
    label: computed(() => $t("Visibility")),
    prop: "visibility",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by visibility"),
      clearable: true
    },
    options: [
      { label: $t("Public"), value: "public" },
      { label: $t("Private"), value: "private" }
    ]
  },
  {
    label: computed(() => $t("Bot Type")),
    prop: "botType",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by bot type"),
      clearable: true
    },
    options: [
      { label: $t("Personal"), value: "personal" },
      { label: $t("Organization"), value: "organization" }
    ]
  },
  {
    label: computed(() => $t("Tool Calling Mode")),
    prop: "toolCallingMode",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by tool calling mode"),
      clearable: true
    },
    options: [
      { label: $t("Auto"), value: "auto" },
      { label: $t("None"), value: "none" },
      { label: $t("Required"), value: "required" }
    ]
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by trash status"),
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: "yes" },
      { label: $t("No"), value: "no" }
    ]
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
};

const handleReset = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
  emit("reset");
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  width: 100%;
}
</style>
