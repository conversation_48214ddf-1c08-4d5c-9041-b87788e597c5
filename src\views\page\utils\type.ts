export type FormItemProps = {
  id?: number | null;
  layouts?: Record<string, any>;
  publishedAt?: string;
  status?: string;
  authorId?: number;
  // Translation fields
  locale?: string;
  title?: string;
  slug?: string;
  gjsComponents?: string;
  gjsStyles?: string;
  gjsHtml?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
};

export type PageFilterProps = {
  layouts?: Record<string, any>;
  status?: string;
  authorId?: number;
  locale?: string;
  title?: string;
  slug?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
