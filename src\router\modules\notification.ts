const Layout = () => import("@/layout/index.vue");

export default {
  path: "/notification",
  name: "Notification",
  component: Layout,
  redirect: "/notification/index",
  meta: {
    icon: "ri/notification-line",
    title: "Notification Management",
    rank: 6
  },
  children: [
    {
      path: "/notification/index",
      name: "NotificationIndex",
      component: () => import("@/views/notification/index.vue"),
      meta: {
        title: "Notification Management",
        showLink: true,
        auths: ["notification:list"]
      }
    }
  ]
} satisfies RouteConfigsTable;
