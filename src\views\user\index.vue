<script setup lang="ts">
import {
  ref,
  nextTick,
  onMounted,
  onUnmounted,
  defineAsyncComponent
} from "vue";
import { useUserHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { hasAuth } from "@/router/utils";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "UserManagement"
});

const UserDrawerForm = defineAsyncComponent(
  () => import("./components/UserDrawerForm.vue")
);
const UserFilterForm = defineAsyncComponent(
  () => import("./components/UserFilterForm.vue")
);

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetUsers,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  userFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore,
  // Advanced operations
  handleStatusChange,
  handleSuspend,
  handleUnsuspend,
  handleBan,
  handleUnban,
  handleSendPasswordReset,
  handleSendEmailVerification,
  handleVerifyEmail,
  // Dialog states
  passwordDialogVisible,
  rolesDialogVisible,
  permissionsDialogVisible,
  selectedUserId,
  // Data loaders
  loadRoles,
  loadPermissions
} = useUserHook();

onMounted(() => {
  nextTick(() => {
    fnGetUsers();
  });
});
</script>

<template>
  <div class="main">
    <PureTableBar
      :title="$t('User Management')"
      :columns="columns"
      @refresh="fnGetUsers"
      @filter="filterVisible = true"
    >
      <template #buttons>
        <el-tooltip :content="$t('Create new')" placement="top">
          <el-button
            type="text"
            class="font-bold text-[16px]"
            :disabled="!hasAuth('user.create')"
            @click="
              () => {
                drawerValues = {};
                drawerVisible = true;
              }
            "
          >
            <IconifyIconOffline
              :icon="useRenderIcon('ri/add-circle-line')"
              class="mr-1"
            />
            {{ $t("Create") }}
          </el-button>
        </el-tooltip>

        <el-popconfirm
          :title="$t('Are you sure you want to delete selected items?')"
          @confirm="handleBulkDelete"
        >
          <template #reference>
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('user.destroy') || multipleSelection.length === 0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/delete-bin-line')"
                class="mr-1"
              />
              {{ $t("Bulk Delete") }}
            </el-button>
          </template>
        </el-popconfirm>

        <el-popconfirm
          :title="$t('Are you sure you want to permanently delete selected items?')"
          @confirm="handleBulkDestroy"
        >
          <template #reference>
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('user.force-delete') || multipleSelection.length === 0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/delete-bin-2-line')"
                class="mr-1"
              />
              {{ $t("Bulk Destroy") }}
            </el-button>
          </template>
        </el-popconfirm>

        <el-button
          type="text"
          class="font-bold text-[16px]"
          :disabled="!hasAuth('user.restore') || multipleSelection.length === 0"
          @click="handleBulkRestore"
        >
          <IconifyIconOffline
            :icon="useRenderIcon('ri/refresh-line')"
            class="mr-1"
          />
          {{ $t("Bulk Restore") }}
        </el-button>
      </template>

      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="records"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="fnHandleSelectionChange"
          @page-size-change="fnHandleSizeChange"
          @page-current-change="fnHandlePageChange"
          @sort-change="fnHandleSortChange"
        >
          <template #operation="{ row }">
            <el-dropdown
              :hide-on-click="false"
              placement="bottom-start"
              trigger="click"
            >
              <el-button type="primary" size="small" link>
                {{ $t("More") }}
                <IconifyIconOffline
                  :icon="useRenderIcon('ri/arrow-down-s-line')"
                />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="primary"
                      size="small"
                      :disabled="!hasAuth('user.read')"
                      @click="
                        () => {
                          drawerValues = clone(row, true);
                          drawerVisible = true;
                        }
                      "
                    >
                      {{ $t("Edit") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item divided>
                    <el-button
                      class="reset-margin"
                      link
                      type="warning"
                      size="small"
                      :disabled="!hasAuth('user.update') || row.status === 'suspended'"
                      @click="handleSuspend(row)"
                    >
                      {{ $t("Suspend") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="success"
                      size="small"
                      :disabled="!hasAuth('user.update') || row.status !== 'suspended'"
                      @click="handleUnsuspend(row)"
                    >
                      {{ $t("Unsuspend") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="danger"
                      size="small"
                      :disabled="!hasAuth('user.update') || row.status === 'banned'"
                      @click="handleBan(row)"
                    >
                      {{ $t("Ban") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="success"
                      size="small"
                      :disabled="!hasAuth('user.update') || row.status !== 'banned'"
                      @click="handleUnban(row)"
                    >
                      {{ $t("Unban") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item divided>
                    <el-button
                      class="reset-margin"
                      link
                      type="info"
                      size="small"
                      :disabled="!hasAuth('user.update')"
                      @click="handleSendPasswordReset(row)"
                    >
                      {{ $t("Send Password Reset") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="info"
                      size="small"
                      :disabled="!hasAuth('user.update') || !!row.email_verified_at"
                      @click="handleSendEmailVerification(row)"
                    >
                      {{ $t("Send Email Verification") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="success"
                      size="small"
                      :disabled="!hasAuth('user.update') || !!row.email_verified_at"
                      @click="handleVerifyEmail(row)"
                    >
                      {{ $t("Verify Email") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item divided>
                    <el-button
                      class="reset-margin"
                      link
                      type="danger"
                      size="small"
                      :disabled="!hasAuth('user.destroy')"
                      @click="handleDelete(row)"
                    >
                      {{ $t("Delete") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="danger"
                      size="small"
                      :disabled="!hasAuth('user.force-delete')"
                      @click="handleDestroy(row)"
                    >
                      {{ $t("Destroy") }}
                    </el-button>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-button
                      class="reset-margin"
                      link
                      type="success"
                      size="small"
                      :disabled="!hasAuth('user.restore')"
                      @click="handleRestore(row)"
                    >
                      {{ $t("Restore") }}
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- Filter Dialog -->
    <el-drawer
      v-model="filterVisible"
      :title="$t('Filter Users')"
      direction="rtl"
      size="400px"
    >
      <UserFilterForm
        ref="filterRef"
        @search="handleFilter"
        @reset="() => { filterVisible = false; fnGetUsers(); }"
      />
    </el-drawer>

    <!-- User Form Dialog -->
    <UserDrawerForm
      v-model:visible="drawerVisible"
      :data="drawerValues"
      @confirm="handleSubmit"
    />
  </div>
</template>

<style scoped>
.main {
  margin: 0;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
}

:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

:deep(.el-button.reset-margin) {
  margin: 0;
}

:deep(.el-dropdown-menu__item) {
  padding: 0 8px;
}
</style>
