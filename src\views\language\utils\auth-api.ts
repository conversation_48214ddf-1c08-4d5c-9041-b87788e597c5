import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/language/utils/type";

export const getLanguages = (params?: object) => {
  return http.request<Result>("get", "/api/auth/languages", {
    params
  });
};

export const getLanguageById = (id: number) => {
  return http.request<Result>("get", `/api/auth/languages/${id}`);
};

export const createLanguage = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/languages", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateLanguageById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/languages/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteLanguageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/languages/${id}`);
};

export const bulkDeleteLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/languages/bulk-delete", {
    data
  });
};

export const destroyLanguageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/languages/${id}`);
};

export const bulkDestroyLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/languages/bulk-destroy", {
    data
  });
};

export const restoreLanguageById = (id: number) => {
  return http.request<Result>("put", `/api/auth/languages/${id}/restore`);
};

export const bulkRestoreLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/languages/bulk-restore", {
    data
  });
};

export const dropdownLanguages = () => {
  return http.request<Result>("get", "/api/auth/languages/dropdown");
};
