<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { $t } from "@/plugins/i18n";
import { ElInput, ElButton, ElPopover, ElUpload } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { websocketService } from "@/services/websocket";
import { useDebounceFn } from "@vueuse/core";
import type { UploadFile } from "element-plus";

interface Props {
  chatId: number;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

interface Emits {
  (e: "send", content: string, contentType?: string, attachments?: any[]): void;
  (e: "typing", isTyping: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  placeholder: "",
  maxLength: 2000
});

const emit = defineEmits<Emits>();

// State
const message = ref("");
const isTyping = ref(false);
const attachments = ref<any[]>([]);
const inputRef = ref();
const uploadRef = ref();
const emojiVisible = ref(false);

// Computed
const placeholderText = computed(() => {
  return props.placeholder || $t("Type a message...");
});

const canSend = computed(() => {
  return (
    !props.disabled && (message.value.trim() || attachments.value.length > 0)
  );
});

const characterCount = computed(() => {
  return message.value.length;
});

const isOverLimit = computed(() => {
  return characterCount.value > props.maxLength;
});

// Emoji data
const emojis = [
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "😒",
  "😞",
  "😔",
  "😟",
  "😕",
  "🙁",
  "☹️",
  "😣",
  "😖",
  "😫",
  "😩",
  "🥺",
  "😢",
  "😭",
  "😤",
  "😠",
  "😡",
  "🤬",
  "🤯",
  "😳",
  "🥵",
  "🥶",
  "😱",
  "😨",
  "😰",
  "😥",
  "😓",
  "🤗",
  "🤔",
  "🤭",
  "🤫",
  "🤥",
  "😶",
  "😐",
  "😑",
  "😬",
  "🙄",
  "😯",
  "😦",
  "😧",
  "😮",
  "😲",
  "🥱",
  "😴",
  "🤤",
  "😪",
  "😵",
  "🤐",
  "🥴",
  "🤢",
  "🤮",
  "🤧",
  "😷",
  "🤒",
  "🤕",
  "🤑",
  "🤠",
  "😈",
  "👍",
  "👎",
  "👌",
  "✌️",
  "🤞",
  "🤟",
  "🤘",
  "🤙",
  "👈",
  "👉",
  "👆",
  "🖕",
  "👇",
  "☝️",
  "👋",
  "🤚",
  "🖐️",
  "✋",
  "🖖",
  "👏",
  "🙌",
  "🤲",
  "🤝",
  "🙏",
  "✍️",
  "💪",
  "🦾",
  "🦿",
  "🦵",
  "🦶",
  "❤️",
  "🧡",
  "💛",
  "💚",
  "💙",
  "💜",
  "🖤",
  "🤍",
  "🤎",
  "💔",
  "❣️",
  "💕",
  "💞",
  "💓",
  "💗",
  "💖",
  "💘",
  "💝",
  "💟",
  "☮️",
  "✝️",
  "☪️",
  "🕉️",
  "☸️",
  "✡️",
  "🔯",
  "🕎",
  "☯️",
  "☦️",
  "🛐"
];

// Debounced typing handler
const debouncedStopTyping = useDebounceFn(() => {
  if (isTyping.value) {
    isTyping.value = false;
    websocketService.sendTyping(props.chatId, false);
    emit("typing", false);
  }
}, 1000);

// Methods
const handleInput = () => {
  if (!isTyping.value && message.value.trim()) {
    isTyping.value = true;
    websocketService.sendTyping(props.chatId, true);
    emit("typing", true);
  }

  if (message.value.trim()) {
    debouncedStopTyping();
  } else if (isTyping.value) {
    isTyping.value = false;
    websocketService.sendTyping(props.chatId, false);
    emit("typing", false);
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

const sendMessage = () => {
  if (!canSend.value || isOverLimit.value) return;

  const content = message.value.trim();
  const messageAttachments = [...attachments.value];

  if (content || messageAttachments.length > 0) {
    emit("send", content, "text", messageAttachments);

    // Clear input
    message.value = "";
    attachments.value = [];

    // Stop typing
    if (isTyping.value) {
      isTyping.value = false;
      websocketService.sendTyping(props.chatId, false);
      emit("typing", false);
    }

    // Focus input
    inputRef.value?.focus();
  }
};

const insertEmoji = (emoji: string) => {
  const cursorPosition =
    inputRef.value?.ref?.selectionStart || message.value.length;
  const before = message.value.substring(0, cursorPosition);
  const after = message.value.substring(cursorPosition);

  message.value = before + emoji + after;
  emojiVisible.value = false;

  // Focus and set cursor position
  setTimeout(() => {
    inputRef.value?.focus();
    const newPosition = cursorPosition + emoji.length;
    inputRef.value?.ref?.setSelectionRange(newPosition, newPosition);
  }, 0);
};

const handleFileUpload = (file: UploadFile) => {
  const attachment = {
    id: Date.now(),
    name: file.name,
    size: file.size,
    type: file.raw?.type,
    url: URL.createObjectURL(file.raw!),
    file: file.raw
  };

  attachments.value.push(attachment);
  return false; // Prevent auto upload
};

const removeAttachment = (index: number) => {
  const attachment = attachments.value[index];
  if (attachment.url.startsWith("blob:")) {
    URL.revokeObjectURL(attachment.url);
  }
  attachments.value.splice(index, 1);
};

const focusInput = () => {
  inputRef.value?.focus();
};

// Lifecycle
onMounted(() => {
  focusInput();
});

onUnmounted(() => {
  // Clean up blob URLs
  attachments.value.forEach(attachment => {
    if (attachment.url.startsWith("blob:")) {
      URL.revokeObjectURL(attachment.url);
    }
  });

  // Stop typing if still typing
  if (isTyping.value) {
    websocketService.sendTyping(props.chatId, false);
  }
});

// Watch for disabled state
watch(
  () => props.disabled,
  disabled => {
    if (disabled && isTyping.value) {
      isTyping.value = false;
      websocketService.sendTyping(props.chatId, false);
      emit("typing", false);
    }
  }
);

// Expose methods
defineExpose({
  focusInput,
  clearMessage: () => {
    message.value = "";
  },
  insertText: (text: string) => {
    message.value += text;
  }
});
</script>

<template>
  <div class="message-composer">
    <!-- Attachments Preview -->
    <div v-if="attachments.length > 0" class="attachments-preview">
      <div
        v-for="(attachment, index) in attachments"
        :key="attachment.id"
        class="attachment-item"
      >
        <div class="attachment-info">
          <IconifyIconOffline
            :icon="useRenderIcon('ri/file-line')"
            class="attachment-icon"
          />
          <span class="attachment-name">{{ attachment.name }}</span>
          <span class="attachment-size">
            ({{ Math.round(attachment.size / 1024) }}KB)
          </span>
        </div>
        <el-button type="text" size="small" @click="removeAttachment(index)">
          <IconifyIconOffline :icon="useRenderIcon('ri/close-line')" />
        </el-button>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-area">
      <!-- File Upload -->
      <el-upload
        ref="uploadRef"
        :before-upload="handleFileUpload"
        :show-file-list="false"
        :disabled="disabled"
        accept="*"
        class="upload-button"
      >
        <el-button
          type="text"
          size="large"
          :disabled="disabled"
          class="action-button"
        >
          <IconifyIconOffline :icon="useRenderIcon('ri/attachment-line')" />
        </el-button>
      </el-upload>

      <!-- Message Input -->
      <div class="input-wrapper">
        <el-input
          ref="inputRef"
          v-model="message"
          type="textarea"
          :placeholder="placeholderText"
          :disabled="disabled"
          :maxlength="maxLength"
          :show-word-limit="false"
          :autosize="{ minRows: 1, maxRows: 4 }"
          resize="none"
          class="message-input"
          @input="handleInput"
          @keydown="handleKeydown"
        />

        <!-- Character Count -->
        <div
          v-if="characterCount > 0"
          :class="['character-count', isOverLimit ? 'over-limit' : '']"
        >
          {{ characterCount }}/{{ maxLength }}
        </div>
      </div>

      <!-- Emoji Button -->
      <el-popover
        v-model:visible="emojiVisible"
        placement="top"
        width="320"
        trigger="click"
        :disabled="disabled"
      >
        <template #reference>
          <el-button
            type="text"
            size="large"
            :disabled="disabled"
            class="action-button"
          >
            <IconifyIconOffline :icon="useRenderIcon('ri/emotion-line')" />
          </el-button>
        </template>

        <div class="emoji-picker">
          <div class="emoji-grid">
            <button
              v-for="emoji in emojis"
              :key="emoji"
              class="emoji-button"
              @click="insertEmoji(emoji)"
            >
              {{ emoji }}
            </button>
          </div>
        </div>
      </el-popover>

      <!-- Send Button -->
      <el-button
        type="primary"
        size="large"
        :disabled="!canSend || isOverLimit"
        class="send-button"
        @click="sendMessage"
      >
        <IconifyIconOffline :icon="useRenderIcon('ri/send-plane-line')" />
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.message-composer {
  border-top: 1px solid #f0f0f0;
  background: white;
}

.attachments-preview {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.attachment-icon {
  color: #666;
  font-size: 16px;
}

.attachment-name {
  font-weight: 500;
  truncate: true;
}

.attachment-size {
  color: #999;
  font-size: 12px;
}

.input-area {
  display: flex;
  align-items: flex-end;
  padding: 12px 16px;
  gap: 8px;
}

.upload-button {
  flex-shrink: 0;
}

.input-wrapper {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
}

:deep(.message-input .el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 20px;
  padding: 10px 16px;
  line-height: 1.4;
  resize: none;
  font-size: 14px;
}

:deep(.message-input .el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.character-count {
  position: absolute;
  bottom: 4px;
  right: 12px;
  font-size: 11px;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 4px;
}

.character-count.over-limit {
  color: #f56c6c;
}

.action-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #666;
  transition: all 0.2s ease;
}

.action-button:hover:not(:disabled) {
  background: #f0f0f0;
  color: #409eff;
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0;
}

.emoji-picker {
  max-height: 200px;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 8px;
}

.emoji-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 18px;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.emoji-button:hover {
  background: #f0f0f0;
}

/* Responsive */
@media (max-width: 768px) {
  .input-area {
    padding: 8px 12px;
    gap: 6px;
  }

  .action-button,
  .send-button {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  :deep(.message-input .el-textarea__inner) {
    padding: 8px 12px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
</style>
