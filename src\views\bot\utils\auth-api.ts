import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/bot/utils/type";

// Bot Management APIs

// 1.1 Get public bots (no authentication required)
export const getPublicBots = (params?: object) => {
  return http.request<Result>("get", "/api/bots/public", {
    params
  });
};

// 1.2 Get user's bots (authentication required)
export const getBots = (params?: object) => {
  return http.request<Result>("get", "/api/auth/bots", {
    params
  });
};

// 1.4 Get bot details by UUID
export const getBotById = (uuid: string) => {
  return http.request<Result>("get", `/api/auth/bots/${uuid}`);
};

// 1.3 Create new bot
export const createBot = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/bots", {
    data: useConvertKeyToSnake(data),
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

// 1.5 Update bot by UUID
export const updateBotById = (uuid: string, data: FormItemProps) => {
  return http.request<Result>("post", `/api/auth/bots/${uuid}`, {
    data: useConvertKeyToSnake(data),
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

// 1.6 Search bots
export const searchBots = (params: {
  query: string;
  include_public?: boolean;
  per_page?: number;
}) => {
  return http.request<Result>("get", "/api/auth/bots/search", {
    params
  });
};

// Legacy delete/restore functions (keeping for backward compatibility)
export const deleteBotById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/bots/${id}`);
};

export const bulkDeleteBots = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/bots/bulk", {
    data: { ids }
  });
};

export const destroyBotById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/bots/${id}/force`);
};

export const bulkDestroyBots = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/bots/bulk/force", {
    data: { ids }
  });
};

export const restoreBotById = (id: number) => {
  return http.request<Result>("post", `/api/auth/bots/${id}/restore`);
};

export const bulkRestoreBots = (ids: number[]) => {
  return http.request<Result>("post", "/api/auth/bots/bulk/restore", {
    data: { ids }
  });
};

// Supporting APIs

// Get AI Models for dropdown
export const getAiModels = () => {
  return http.request<Result>("get", "/api/auth/model-ai");
};

export const getAiModelDropdown = () => {
  return http.request<Result>("get", `/api/model-ai/dropdown`);
};

// 5.1 Get general prompt for bot
export const getGeneralPrompts = (params?: object) => {
  return http.request<Result>("get", `/api/auth/bot-general-prompt`, {
    params
  });
};
