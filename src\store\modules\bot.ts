import { defineStore } from "pinia";
import {
  getBots,
  createBot,
  updateBotById,
  deleteBotById,
  bulkDeleteBots,
  restoreBotById,
  bulkRestoreBots,
  destroyBotById,
  bulkDestroyBots
} from "@/views/bot/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { ElMessage, ElNotification } from "element-plus";
import type { FormItemProps, BotFilterProps } from "@/views/bot/utils/type";
import { $t } from "@/plugins/i18n";

export interface BotState {
  bots: FormItemProps[];
  loading: boolean;
  filters: BotFilterProps;
  selectedBots: FormItemProps[];
  viewMode: "grid" | "list";
  sortBy: "updated" | "created" | "name" | "status";
  searchQuery: string;
  selectedCategory: string;
}

export const useBotStore = defineStore("bot-management", {
  state: (): BotState => ({
    bots: [] as FormItemProps[],
    loading: false,
    filters: {},
    selectedBots: [],
    viewMode: "grid",
    sortBy: "updated",
    searchQuery: "",
    selectedCategory: "all"
  }),

  getters: {
    // Filtered and sorted bots
    filteredBots: state => {
      let filtered = [...state.bots];

      // Filter by category
      if (state.selectedCategory !== "all") {
        filtered = filtered.filter(
          bot => bot.botType === state.selectedCategory
        );
      }

      // Filter by search query
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(
          bot =>
            bot.name?.toLowerCase().includes(query) ||
            bot.description?.toLowerCase().includes(query)
        );
      }

      // Apply additional filters
      if (state.filters.status) {
        filtered = filtered.filter(bot => bot.status === state.filters.status);
      }

      if (state.filters.visibility) {
        filtered = filtered.filter(
          bot => bot.visibility === state.filters.visibility
        );
      }

      // Sort
      filtered.sort((a, b) => {
        switch (state.sortBy) {
          case "name":
            return (a.name || "").localeCompare(b.name || "");
          case "created":
            return (
              new Date(b.createdAt || 0).getTime() -
              new Date(a.createdAt || 0).getTime()
            );
          case "status":
            return (a.status || "").localeCompare(b.status || "");
          default: // updated
            return (
              new Date(b.updatedAt || 0).getTime() -
              new Date(a.updatedAt || 0).getTime()
            );
        }
      });

      return filtered;
    },

    // Categories with counts
    categories: state => {
      const categories = [
        { value: "all", label: $t("All"), count: state.bots.length }
      ];

      const botTypes = [
        ...new Set(state.bots.map(bot => bot.botType).filter(Boolean))
      ];

      botTypes.forEach(type => {
        const count = state.bots.filter(bot => bot.botType === type).length;
        categories.push({
          value: type,
          label: type,
          count
        });
      });

      return categories;
    },

    // Check if any bots are selected
    hasSelectedBots: state => state.selectedBots.length > 0,

    // Get bot by ID
    getBotById: state => (id: string) => {
      return state.bots.find(bot => bot.id.toString() == id);
    }
  },

  actions: {
    // Fetch bots from API
    async fetchBots() {
      this.loading = true;
      try {
        const { data, success } = await getBots();
        if (success) {
          this.bots = useConvertKeyToCamel(data || []);
        }
      } catch (error) {
        console.error("Error fetching bots:", error);
        ElMessage.error("Không thể tải danh sách Bot");
      } finally {
        this.loading = false;
      }
    },

    // Create new bot
    async createBot(botData: FormItemProps) {
      this.loading = true;
      try {
        const { data } = await createBot(botData);

        if (data?.success) {
          const newBot = useConvertKeyToCamel(data.data);
          this.bots.unshift(newBot);
          ElNotification.success({
            title: "Thành công",
            message: "Đã tạo Bot mới thành công!"
          });
          return newBot;
        }
      } catch (error) {
        console.error("Error creating bot:", error);
        ElMessage.error("Không thể tạo Bot mới");
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Update bot
    async updateBot(id: string, botData: FormItemProps) {
      this.loading = true;
      try {
        const { data } = await updateBotById(id, botData);

        if (data?.success) {
          const updatedBot = useConvertKeyToCamel(data.data);
          const index = this.bots.findIndex(bot => bot.id.toString() == id);
          if (index > -1) {
            this.bots[index] = updatedBot;
          }
          ElMessage.success("Đã cập nhật Bot thành công!");
          return updatedBot;
        }
      } catch (error) {
        console.error("Error updating bot:", error);
        ElMessage.error("Không thể cập nhật Bot");
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Delete bot (soft delete)
    async deleteBot(id: number) {
      try {
        const { data } = await deleteBotById(id);

        if (data?.success) {
          const index = this.bots.findIndex(bot => bot.id === id);
          if (index > -1) {
            this.bots.splice(index, 1);
          }
          ElMessage.success("Đã xóa Bot thành công!");
        }
      } catch (error) {
        console.error("Error deleting bot:", error);
        ElMessage.error("Không thể xóa Bot");
        throw error;
      }
    },

    // Restore bot
    async restoreBot(id: number) {
      try {
        const { data } = await restoreBotById(id);

        if (data?.success) {
          await this.fetchBots();
          ElMessage.success("Đã khôi phục Bot thành công!");
        }
      } catch (error) {
        console.error("Error restoring bot:", error);
        ElMessage.error("Không thể khôi phục Bot");
        throw error;
      }
    },

    // Permanently delete bot
    async destroyBot(id: number) {
      try {
        const { data } = await destroyBotById(id);

        if (data?.success) {
          const index = this.bots.findIndex(bot => bot.id === id);
          if (index > -1) {
            this.bots.splice(index, 1);
          }
          ElMessage.success("Đã xóa vĩnh viễn Bot!");
        }
      } catch (error) {
        console.error("Error destroying bot:", error);
        ElMessage.error("Không thể xóa vĩnh viễn Bot");
        throw error;
      }
    },

    // Duplicate bot
    async duplicateBot(bot: FormItemProps, newName: string) {
      const duplicatedBot = {
        ...bot,
        id: undefined,
        name: newName,
        status: "draft" as const,
        createdAt: undefined,
        updatedAt: undefined
      };

      return await this.createBot(duplicatedBot);
    },

    // Toggle bot status
    async toggleBotStatus(id: string) {
      const bot = this.getBotById(id);
      if (!bot) return;

      const newStatus = bot.status === "active" ? "paused" : "active";
      await this.updateBot(id, { ...bot, status: newStatus });
    },

    // Bulk operations
    async bulkDelete(ids: number[]) {
      try {
        const { data } = await bulkDeleteBots(ids);

        if (data?.success) {
          this.bots = this.bots.filter(bot => !ids.includes(bot.id!));
          this.selectedBots = [];
          ElMessage.success(`Đã xóa ${ids.length} Bot thành công!`);
        }
      } catch (error) {
        console.error("Error bulk deleting bots:", error);
        ElMessage.error("Không thể xóa các Bot đã chọn");
        throw error;
      }
    },

    async bulkRestore(ids: number[]) {
      try {
        const { data } = await bulkRestoreBots(ids);

        if (data?.success) {
          await this.fetchBots();
          this.selectedBots = [];
          ElMessage.success(`Đã khôi phục ${ids.length} Bot thành công!`);
        }
      } catch (error) {
        console.error("Error bulk restoring bots:", error);
        ElMessage.error("Không thể khôi phục các Bot đã chọn");
        throw error;
      }
    },

    async bulkDestroy(ids: number[]) {
      try {
        const { data } = await bulkDestroyBots(ids);

        if (data?.success) {
          this.bots = this.bots.filter(bot => !ids.includes(bot.id!));
          this.selectedBots = [];
          ElMessage.success(`Đã xóa vĩnh viễn ${ids.length} Bot!`);
        }
      } catch (error) {
        console.error("Error bulk destroying bots:", error);
        ElMessage.error("Không thể xóa vĩnh viễn các Bot đã chọn");
        throw error;
      }
    },

    // UI state management
    setViewMode(mode: "grid" | "list") {
      this.viewMode = mode;
    },

    setSortBy(sortBy: "updated" | "created" | "name" | "status") {
      this.sortBy = sortBy;
    },

    setSearchQuery(query: string) {
      this.searchQuery = query;
    },

    setSelectedCategory(category: string) {
      this.selectedCategory = category;
    },

    setFilters(filters: BotFilterProps) {
      this.filters = { ...this.filters, ...filters };
    },

    clearFilters() {
      this.filters = {};
      this.searchQuery = "";
      this.selectedCategory = "all";
    },

    setSelectedBots(bots: FormItemProps[]) {
      this.selectedBots = bots;
    },

    addSelectedBot(bot: FormItemProps) {
      if (!this.selectedBots.find(b => b.id === bot.id)) {
        this.selectedBots.push(bot);
      }
    },

    removeSelectedBot(botId: number) {
      this.selectedBots = this.selectedBots.filter(bot => bot.id !== botId);
    },

    clearSelectedBots() {
      this.selectedBots = [];
    }
  }
});

export function useBotStoreHook() {
  return useBotStore();
}
