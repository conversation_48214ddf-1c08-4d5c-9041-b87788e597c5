export type AiModel = {
  key: string;
  name: string;
};

export type BotOwner = {
  firstName?: string;
  lastName?: string;
  fullName: string;
  name?: string;
  avatar?: string;
};

export type Conversation = {
  uuid?: string;
  title?: string;
  botUuid?: string;
  status?: "active" | "completed" | "archived";
  messageCount?: number;
  lastMessageAt?: string;
  createdAt?: string;
  updatedAt?: string;
};

export type ChatBot = {
  uuid?: string;
  name?: string;
  logo?: string | null;
  description?: string;
  systemPrompt?: string;
  greetingMessage?: string;
  starterMessages?: (string | null)[];
  closingMessage?: string | null;
  parameters?: Record<string, any> | null;
  toolCallingMode?: "none" | "auto" | "specific";
  visibility?: "private" | "public";
  botType?: "personal" | "team";
  isShareable?: boolean;
  status?: "draft" | "published" | "archived";
  metadata?: Record<string, any> | null;
  aiModel?: AiModel;
  owner?: BotOwner;
  version?: string;
  createdAt?: string;
  updatedAt?: string;
  // Thêm conversation có sẵn từ API
  conversation?: Conversation | null;
};
