import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";

// Chat APIs
export const getChatBots = () => {
  return http.request<Result>("get", "/api/auth/chat/bots");
};

// Conversation Management APIs

// 2.1 Get conversations list
export const getConversations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/conversations", {
    params
  });
};

// 2.2 Create new conversation
export const createConversation = (data: object) => {
  return http.request<Result>("post", "/api/auth/conversations", {
    data: useConvertKeyToSnake(data)
  });
};

// 2.3 Get conversation details by UUID
export const getConversationById = (uuid: string) => {
  return http.request<Result>("get", `/api/v1/auth/conversations/${uuid}`);
};

// 2.4 Search conversations
export const searchConversations = (params: {
  query: string;
  per_page?: number;
}) => {
  return http.request<Result>("get", "/api/v1/auth/conversations/search", {
    params
  });
};

// 2.5 Delete conversation by UUID
export const deleteConversation = (uuid: string) => {
  return http.request<Result>("delete", `/api/v1/auth/conversations/${uuid}`);
};

// 2.6 Update conversation
export const updateConversation = (
  uuid: string,
  data: {
    title?: string;
    status?: string;
  }
) => {
  return http.request<Result>("put", `/api/v1/auth/conversations/${uuid}`, {
    data: useConvertKeyToSnake(data)
  });
};

// Message Management APIs

// 3.1 Get messages in conversation
export const getMessages = (
  conversationUuid: string,
  params?: {
    role?: string;
    status?: string;
    content_type?: string;
    per_page?: number;
  }
) => {
  return http.request<Result>(
    "get",
    `/api/v1/auth/conversations/${conversationUuid}/messages`,
    {
      params
    }
  );
};

// 3.2 Create new message
export const createMessage = (data: {
  conversation_id: number;
  content: string;
  content_type?: string;
}) => {
  return http.request<Result>("post", "/api/v1/auth/messages", {
    data: useConvertKeyToSnake(data)
  });
};

// 3.3 Get message by ID
export const getMessageById = (messageId: number) => {
  return http.request<Result>("get", `/api/v1/auth/messages/${messageId}`);
};

// 3.4 Generate AI response for conversation
export const generateResponse = (
  conversationUuid: string,
  data?: {
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
  }
) => {
  return http.request<Result>(
    "post",
    `/api/v1/auth/conversations/${conversationUuid}/generate-response`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

// 3.5 Send message and get AI response
export const sendAndRespond = (data: {
  conversation_id: number;
  content: string;
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}) => {
  return http.request<Result>(
    "post",
    "/api/v1/auth/messages/send-and-respond",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

// 3.6 Stream AI response (Server-Sent Events)
export const streamResponse = (
  conversationUuid: string,
  data?: {
    temperature?: number;
    max_tokens?: number;
  }
) => {
  return http.request<Result>(
    "post",
    `/api/v1/auth/conversations/${conversationUuid}/stream-response`,
    {
      data: useConvertKeyToSnake(data),
      headers: {
        Accept: "text/event-stream",
        "Cache-Control": "no-cache"
      }
    }
  );
};

// Legacy Chat APIs (keeping for backward compatibility)
export const getChats = (params?: object) => {
  return http.request<Result>("get", "/api/auth/chats", {
    params
  });
};

export const getChatById = (id: number) => {
  return http.request<Result>("get", `/api/auth/chats/${id}`);
};

export const createChat = (data: object) => {
  return http.request<Result>("post", "/api/auth/chats", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateChatById = (id: number, data: object) => {
  return http.request<Result>("put", `/api/auth/chats/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteChatById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/chats/${id}`);
};

export const bulkDeleteChats = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/chats/bulk", {
    data: { ids }
  });
};

export const destroyChatById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/chats/${id}/force`);
};

export const bulkDestroyChats = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/chats/bulk/force", {
    data: { ids }
  });
};

export const restoreChatById = (id: number) => {
  return http.request<Result>("post", `/api/auth/chats/${id}/restore`);
};

export const bulkRestoreChats = (ids: number[]) => {
  return http.request<Result>("post", "/api/auth/chats/bulk/restore", {
    data: { ids }
  });
};

// Get Users for dropdown
export const getUsers = () => {
  return http.request<Result>("get", "/api/auth/users");
};

// Get Bots for dropdown
export const getBots = () => {
  return http.request<Result>("get", "/api/auth/bots");
};

// Export Knowledge Base APIs
export * from "./knowledge-base-api";

// Types for Chat/Conversation APIs
export interface Conversation {
  id: number;
  title: string;
  bot_id: number;
  user_id: number;
  user_type: string;
  status: "active" | "completed" | "archived";
  message_count: number;
  created_at: string;
  updated_at: string;
  bot?: {
    uuid: string;
    name: string;
  };
  user?: {
    id: number;
    name: string;
  };
  messages?: Message[];
}

export interface Message {
  id: number;
  conversation_id: number;
  role: "user" | "assistant" | "system" | "tool";
  content: string;
  content_type: "text" | "image" | "file" | "audio" | "video" | "mixed";
  status: "pending" | "streaming" | "completed" | "failed" | "cancelled";
  model_used?: string;
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost?: number;
  created_at: string;
  updated_at: string;
  conversation?: {
    id: number;
    title: string;
  };
}

export interface ConversationListResponse {
  success: boolean;
  message: string;
  data: Conversation[];
  meta: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
  };
}

export interface ConversationResponse {
  success: boolean;
  message: string;
  data: Conversation;
}

export interface MessageResponse {
  success: boolean;
  message: string;
  data: Message;
}

export interface SendAndRespondResponse {
  success: boolean;
  message: string;
  data: {
    user_message: Message;
    ai_message: Message;
  };
}
