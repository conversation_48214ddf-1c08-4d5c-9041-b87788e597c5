import { defineStore } from "pinia";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import { getChatBots } from "@/views/chat/utils/auth-api";
import type { ChatBot } from "@/views/chat/utils/type";

export type ChatBotState = {
  loading: boolean;
  bots: ChatBot[];
  filters: Record<string, any>;
  selectedBot: ChatBot | null;
};

export const useChatBotStore = defineStore("chat-management", {
  state: (): ChatBotState => {
    return {
      loading: false,
      bots: [],
      filters: {
        searchQuery: ""
      },
      selectedBot: null
    };
  },

  actions: {
    // Fetch bots from API
    async getChatBots() {
      this.loading = true;
      try {
        const { data, success } = await getChatBots();
        if (success) {
          this.bots = useConvertKeyToCamel(data || []);
        }
      } catch (error) {
        console.error("Error fetching bots:", error);
      } finally {
        this.loading = false;
      }
    },
    getSelectedChatBot(uuid: string) {
      if (!uuid) {
        this.selectedBot = null;
        return;
      }
      this.selectedBot = this.bots.find((bot: any) => bot.uuid == uuid) || null;
    }
  }
});

export function useChatBotStoreHook() {
  return useChatBotStore();
}
