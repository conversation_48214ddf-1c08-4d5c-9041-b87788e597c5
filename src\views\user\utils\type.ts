export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  name?: string;
  email?: string;
  emailVerifiedAt?: string;
  password?: string;
  passwordConfirmation?: string;
  avatar?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: string;
  bio?: string;
  timezone?: string;
  locale?: string;
  status?: 'active' | 'inactive' | 'suspended' | 'banned';
  lastLoginAt?: string;
  loginCount?: number;
  metadata?: any;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  // Relationship data
  roles?: {
    id: number;
    name: string;
    displayName: string;
  }[];
  permissions?: {
    id: number;
    name: string;
    displayName: string;
  }[];
};

export type UserFilterProps = {
  name?: string;
  email?: string;
  status?: 'active' | 'inactive' | 'suspended' | 'banned';
  gender?: 'male' | 'female' | 'other';
  emailVerified?: boolean;
  roleId?: number;
  dateFrom?: string;
  dateTo?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
