import { reactive, ref } from "vue";
import {
  getMessages,
  createMessage,
  updateMessageById,
  deleteMessageById,
  bulkDeleteMessages,
  bulkDestroyMessages,
  destroyMessageById,
  restoreMessageById,
  bulkRestoreMessages
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { MessageFilterProps } from "@/views/message/utils/type";

export function useMessageHook() {
  // State management
  const loading = ref(false);
  const filterRef = ref();
  const records = ref([]);
  const multipleSelection = ref([]);
  const sort = ref({ prop: "createdAt", order: "descending" });

  // Pagination
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    background: true,
    pageSizes: [20, 50, 100, 200]
  });

  // Form states
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref({});
  const messageFormRef = ref();

  // API Handlers
  const fnGetMessages = async (params?: MessageFilterProps) => {
    loading.value = true;
    try {
      const queryParams = {
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        sort_by: sort.value.prop,
        sort_order: sort.value.order === "ascending" ? "asc" : "desc",
        ...params
      };

      const { data } = await getMessages(queryParams);
      
      if (data?.success) {
        records.value = useConvertKeyToCamel(data.data.data || []);
        pagination.total = data.data.total || 0;
        pagination.currentPage = data.data.current_page || 1;
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
      message($t("Failed to fetch messages"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnHandleCreateMessage = async (data: FieldValues) => {
    try {
      const response = await createMessage(data);
      if (response.data?.success) {
        message($t("Message created successfully"), { type: "success" });
        drawerVisible.value = false;
        fnGetMessages();
        return true;
      }
    } catch (error) {
      console.error("Error creating message:", error);
      message($t("Failed to create message"), { type: "error" });
    }
    return false;
  };

  const fnHandleUpdateMessage = async (id: number, data: FieldValues) => {
    try {
      const response = await updateMessageById(id, data);
      if (response.data?.success) {
        message($t("Message updated successfully"), { type: "success" });
        drawerVisible.value = false;
        fnGetMessages();
        return true;
      }
    } catch (error) {
      console.error("Error updating message:", error);
      message($t("Failed to update message"), { type: "error" });
    }
    return false;
  };

  const fnHandleDelete = async (id: number) => {
    try {
      const response = await deleteMessageById(id);
      if (response.data?.success) {
        message($t("Message deleted successfully"), { type: "success" });
        fnGetMessages();
        return true;
      }
    } catch (error) {
      console.error("Error deleting message:", error);
      message($t("Failed to delete message"), { type: "error" });
    }
    return false;
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      const response = await bulkDeleteMessages(ids);
      if (response.data?.success) {
        message($t("Messages deleted successfully"), { type: "success" });
        fnGetMessages();
        return true;
      }
    } catch (error) {
      console.error("Error bulk deleting messages:", error);
      message($t("Failed to delete messages"), { type: "error" });
    }
    return false;
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    sort.value = { prop, order };
    fnGetMessages();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetMessages();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetMessages();
  };

  // UI Action Handlers
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this message?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(row.id);
    } catch {
      // User cancelled
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected messages?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await fnHandleBulkDelete(ids);
    } catch {
      // User cancelled
    }
  };

  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this message?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );
      const response = await destroyMessageById(row.id);
      if (response.data?.success) {
        message($t("Message permanently deleted"), { type: "success" });
        fnGetMessages();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to permanently delete message"), { type: "error" });
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected messages?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDestroyMessages(ids);
      if (response.data?.success) {
        message($t("Messages permanently deleted"), { type: "success" });
        fnGetMessages();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to permanently delete messages"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      const response = await restoreMessageById(row.id);
      if (response.data?.success) {
        message($t("Message restored successfully"), { type: "success" });
        fnGetMessages();
      }
    } catch (error) {
      message($t("Failed to restore message"), { type: "error" });
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestoreMessages(ids);
      if (response.data?.success) {
        message($t("Messages restored successfully"), { type: "success" });
        fnGetMessages();
      }
    } catch (error) {
      message($t("Failed to restore messages"), { type: "error" });
    }
  };

  // Form Handlers
  const handleSubmit = async (values: FieldValues) => {
    const isEdit = !!values.id;
    const success = isEdit
      ? await fnHandleUpdateMessage(values.id, values)
      : await fnHandleCreateMessage(values);
    
    if (success && messageFormRef.value?.resetForm) {
      messageFormRef.value.resetForm();
    }
  };

  const handleFilter = async (values: FieldValues) => {
    filterVisible.value = false;
    pagination.currentPage = 1;
    await fnGetMessages(values);
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    messageFormRef,

    // API Handlers
    fnGetMessages,
    fnHandleCreateMessage,
    fnHandleUpdateMessage,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
