const Layout = () => import("@/layout/index.vue");

export default {
  path: "/bots/management",
  name: "<PERSON><PERSON>",
  redirect: "/bots",
  component: Layout,
  meta: {
    icon: "ri/robot-2-line",
    title: "Bot Management",
    rank: 4
  },
  children: [
    {
      path: "/bots",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      component: () => import("@/views/bot/index.vue"),
      meta: {
        title: "My Bots",
        showLink: true
      }
    },
    {
      path: "/bots/agent",
      name: "BotAgentCreate",
      component: () => import("@/views/bot/agent.vue"),
      meta: {
        title: "Create or Edit Bot",
        showLink: false,
        activePath: "/bots"
      }
    },
    {
      path: "/bots/agent/:uuid?",
      name: "BotAgentEdit",
      component: () => import("@/views/bot/agent.vue"),
      meta: {
        title: "Bot Edit",
        showLink: false,
        activePath: "/bots"
      }
    }
  ]
} satisfies RouteConfigsTable;
