const Layout = () => import("@/layout/index.vue");

export default {
  path: "/dashboard",
  name: "Dashboard",
  component: Layout,
  redirect: "/dashboard/overview",
  meta: {
    icon: "ri/dashboard-line",
    title: "Dashboard",
    rank: 1
  },
  children: [
    {
      path: "/dashboard/overview",
      name: "DashboardOverview",
      component: () => import("@/views/dashboard/index.vue"),
      meta: {
        title: "Dashboard Overview",
        showLink: true,
        auths: ["dashboard.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
