<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { Promotion } from "@element-plus/icons-vue";

interface Props {
  mainView?: string;
  chatBot: any; // Chat bot instance từ parent
}

interface Emits {
  (e: "showHistoryView", showCurrentAgentHistory?: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Sử dụng chatBot từ props thay vì hook
const {
  messages,
  currentConversationId,
  newMessage,
  isTyping,
  attachedFiles,
  selectedAgent,
  currentMessages,
  showStarterPrompts,
  sendMessage,
  sendStarterPrompt,
  clearMessages,
  deleteCurrentConversation,
  removeAttachment,
  scrollToBottom,
  setupWebSocket,
  cleanupWebSocket
} = props.chatBot;

const chatMessagesContainer = ref<HTMLElement | null>(null);
const clientHeight = ref(400);

// Tools system
const toolsMasterSwitch = ref(true);
const availableTools = ref([
  {
    id: "weather",
    name: "Ki<PERSON><PERSON> tra thời tiết",
    enabled: false,
    icon: "ri:sun-line"
  },
  {
    id: "calendar",
    name: "Tạo sự kiện lịch",
    enabled: true,
    icon: "ri:calendar-line"
  },
  {
    id: "search",
    name: "Tìm kiếm web",
    enabled: false,
    icon: "ri:search-line"
  },
  {
    id: "calculator",
    name: "Máy tính",
    enabled: true,
    icon: "ri:calculator-line"
  }
]);

const showHistoryView = (showCurrentAgentHistory = false) => {
  emit("showHistoryView", showCurrentAgentHistory);
};

const handleEnter = (event: KeyboardEvent) => {
  if (!event.shiftKey) {
    sendMessage();
  }
};

const updateChatContainerHeight = () => {
  if (chatMessagesContainer.value) {
    const header = document.querySelector(".chat-header");
    const footer = document.querySelector(".chat-footer");
    const headerHeight = header ? header.getBoundingClientRect().height : 81;
    const footerHeight = footer ? footer.getBoundingClientRect().height : 125;
    const parentHeight = (chatMessagesContainer.value as HTMLElement)
      .parentElement.clientHeight;
    const newHeight = parentHeight - headerHeight - footerHeight;
    clientHeight.value = newHeight > 0 ? newHeight : 400;
  }
};

onMounted(() => {
  nextTick(() => {
    updateChatContainerHeight();
  });
  window.addEventListener("resize", updateChatContainerHeight);

  // Setup WebSocket listener
  setupWebSocket();
});

onUnmounted(() => {
  window.removeEventListener("resize", updateChatContainerHeight);

  // Cleanup WebSocket listener
  cleanupWebSocket();
});
</script>

<template>
  <div
    v-if="props.mainView === 'chat' && selectedAgent"
    class="chat-area w-full h-full relative bg-white"
  >
    <header class="chat-header flex items-center justify-between">
      <div class="flex items-center">
        <img
          :src="selectedAgent.logo"
          alt="logo"
          class="w-10 h-10 rounded-full mr-4 object-cover"
        />
        <div>
          <h2 class="text-lg font-semibold text-gray-900">
            {{ selectedAgent.name }}
          </h2>
          <p class="text-sm text-gray-500">
            Đang sử dụng:
            <span class="font-medium text-gray-700">
              {{ selectedAgent.aiModel?.name }}
            </span>
          </p>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <el-button
          :icon="useRenderIcon('ri:history-line')"
          circle
          @click="showHistoryView(true)"
        />
        <el-dropdown trigger="click">
          <el-button :icon="useRenderIcon('ri:more-2-fill')" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                :icon="useRenderIcon('ri:refresh-line')"
                @click="clearMessages"
              >
                Xóa tin nhắn
              </el-dropdown-item>
              <el-dropdown-item
                :icon="useRenderIcon('ri:delete-bin-line')"
                divided
                class="!text-red-500"
                @click="deleteCurrentConversation"
              >
                Xóa cuộc trò chuyện
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <div
      ref="chatMessagesContainer"
      class="chat-messages p-6 overflow-y-auto space-y-6"
      :style="{ height: clientHeight + 'px', maxHeight: clientHeight + 'px' }"
    >
      <!-- SỬA LỖI: Lặp qua `currentMessages` (mảng tin nhắn của agent hiện tại) thay vì `messages` (object) -->
      <div
        v-for="message in currentMessages"
        :key="message.id"
        class="flex"
        :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
      >
        <div
          class="flex items-end max-w-lg"
          :class="{ 'flex-row-reverse': message.role === 'user' }"
        >
          <img
            :src="
              message.role === 'user'
                ? 'https://placehold.co/40x40/E2E8F0/4A5568?text=U'
                : selectedAgent.logo
            "
            alt="logo"
            class="w-8 h-8 rounded-full object-cover"
            :class="message.role === 'user' ? 'ml-3' : 'mr-3'"
          />
          <div
            class="p-3 rounded-2xl shadow-sm"
            :class="{
              'bg-blue-500 text-white rounded-br-none': message.role === 'user',
              'bg-white text-gray-800 rounded-bl-none':
                message.role === 'assistant'
            }"
          >
            <p class="text-sm whitespace-pre-wrap">{{ message.content }}</p>
          </div>
        </div>
      </div>

      <!-- Typing Indicator -->
      <div v-if="isTyping" class="flex justify-start">
        <div class="flex items-end max-w-lg">
          <img
            :src="selectedAgent.logo"
            class="w-8 h-8 rounded-full object-cover mr-3"
            alt="logo"
          />
          <div
            class="p-3 rounded-2xl shadow-sm bg-white text-gray-800 rounded-bl-none"
          >
            <div class="flex items-center space-x-1 typing-indicator">
              <span
                class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                style="animation-delay: -0.3s"
              />
              <span
                class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                style="animation-delay: -0.15s"
              />
              <span class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" />
            </div>
          </div>
        </div>
      </div>

      <!-- Starter Prompts -->
      <div
        v-if="showStarterPrompts"
        class="flex flex-col items-center justify-center pt-4"
      >
        <div class="flex flex-wrap justify-center gap-2 max-w-lg">
          <el-button
            v-for="(prompt, index) in selectedAgent.starterMessages"
            :key="`prompt-${index}`"
            round
            @click="sendStarterPrompt(prompt)"
          >
            {{ prompt }}
          </el-button>
        </div>
      </div>
    </div>

    <footer class="chat-footer !absolute bottom-0 left-0 right-0">
      <div v-if="attachedFiles.length > 0" class="mb-2 flex flex-wrap gap-2">
        <el-tag
          v-for="(file, index) in attachedFiles"
          :key="file.uid"
          closable
          @close="removeAttachment(index)"
        >
          {{ file.name }}
        </el-tag>
      </div>
      <div class="composer-wrapper">
        <el-input
          v-model="newMessage"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 6 }"
          placeholder="Nhập tin nhắn..."
          class="composer-textarea"
          :disabled="isTyping"
          @keyup.enter.prevent="handleEnter"
        />
        <div class="flex justify-between items-center mt-2">
          <div class="flex items-center gap-2">
            <el-upload
              v-model:file-list="attachedFiles"
              action="#"
              :show-file-list="false"
              :auto-upload="false"
              multiple
            >
              <el-button
                :icon="useRenderIcon('ri:attachment-2')"
                circle
                plain
                size="small"
              />
            </el-upload>

            <div class="tool-button-group">
              <el-button
                :icon="useRenderIcon('ri:magic-line')"
                plain
                size="small"
              />
              <el-popover
                placement="top-start"
                :width="280"
                trigger="click"
                popper-style="border-radius: 12px; padding: 1rem;"
              >
                <template #reference>
                  <el-button
                    :icon="useRenderIcon('mdi:tools')"
                    plain
                    size="small"
                  />
                </template>
                <div>
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold text-sm text-gray-800">
                      Kích hoạt Công cụ
                    </h4>
                    <el-switch v-model="toolsMasterSwitch" size="small" />
                  </div>
                  <div v-if="toolsMasterSwitch" class="space-y-1 mt-3">
                    <div
                      v-for="tool in availableTools"
                      :key="tool.id"
                      class="flex items-center justify-between p-1.5 hover:bg-gray-50 rounded-md"
                    >
                      <div class="flex items-center">
                        <el-icon class="mr-2 text-gray-400">
                          <component :is="useRenderIcon(tool.icon)" />
                        </el-icon>
                        <span class="text-sm text-gray-700">
                          {{ tool.name }}
                        </span>
                      </div>
                      <el-switch v-model="tool.enabled" size="small" />
                    </div>
                  </div>
                </div>
              </el-popover>
              <el-button
                :icon="useRenderIcon('ri:camera-line')"
                plain
                size="small"
              />
            </div>
          </div>
          <el-button
            type="primary"
            :icon="Promotion"
            circle
            :disabled="
              (!newMessage.trim() && attachedFiles.length === 0) || isTyping
            "
            @click="sendMessage"
          />
        </div>
      </div>
    </footer>
  </div>
</template>

<style lang="scss">
.chat-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.chat-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem;
  min-height: auto;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  max-height: 400px;
  scroll-behavior: smooth;

  // Custom scrollbar
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }
}

.chat-footer {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem;
}

.composer-wrapper {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 1.25rem;
  padding: 0.75rem;
  transition:
    border-color 0.3s,
    box-shadow 0.3s;

  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
}

// Block 1
.composer-textarea {
  :deep(.el-textarea__inner) {
    border: none;
    box-shadow: none;
    resize: none;
    padding: 0;
  }
}

// Block 2 (duplicate)
.composer-textarea .el-textarea__inner {
  background-color: transparent;
  box-shadow: none !important;
  padding: 0;
  resize: none;
  line-height: 1.5;
}

.tool-button-group {
  display: inline-flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
  padding: 2px;

  .el-button.is-plain {
    border: none;
    margin: 0;
    padding-left: 10px;
    padding-right: 10px;
  }
}

// Typing indicator animation
.typing-indicator span {
  animation: bounce 1.4s infinite ease-in-out both;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// Message animations
.flex {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dropdown menu styling
.el-dropdown-menu__item .el-icon {
  margin-right: 8px;
}

// Responsive
@media (max-width: 768px) {
  .chat-messages {
    padding: 1rem;
  }

  .chat-footer {
    padding: 0.75rem;
  }
}
</style>
