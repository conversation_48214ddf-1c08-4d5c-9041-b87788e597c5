import { ref, reactive, computed, nextTick, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useChatBotStoreHook } from "@/store/modules/chat";
import { websocketService } from "@/services/websocket";
import {
  sendAndRespond,
  createConversation,
  deleteConversation,
  getConversations,
  getMessages
} from "@/views/chat/utils/auth-api";

export function useChatBot() {
  // Store
  const chatBotStore = useChatBotStoreHook();

  // State management
  const messages = reactive<{ [conversationId: string]: any[] }>({});
  const currentConversationId = ref<string | null>(null);
  const newMessage = ref("");
  const isTyping = ref(false);
  const attachedFiles = ref([]);
  const searchQuery = ref("");
  const filterType = ref("all");
  const conversations = ref<{ [botUuid: string]: any[] }>({});

  // Computed properties
  const selectedAgent = computed(() => chatBotStore.selectedBot);
  const agents = computed(() => [...chatBotStore.bots]);

  const currentMessages = computed(() => {
    if (!currentConversationId.value) return [];
    return messages[currentConversationId.value] || [];
  });

  const showStarterPrompts = computed(() => {
    if (!selectedAgent.value) return false;

    // Hiển thị starter prompts khi:
    // 1. Không có conversation hiện tại (chưa bắt đầu chat)
    // 2. Hoặc conversation chỉ có greeting message
    if (!currentConversationId.value) return true;

    return (
      currentMessages.value.length === 1 &&
      currentMessages.value[0].role === "assistant"
    );
  });

  const filteredAgents = computed(() => {
    let agentsToFilter = agents.value;
    if (filterType.value !== "all") {
      agentsToFilter = agentsToFilter.filter(
        agent => agent.botType === filterType.value
      );
    }
    if (!searchQuery.value) {
      return agentsToFilter;
    }
    return agentsToFilter.filter(
      agent =>
        agent.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        agent.description
          .toLowerCase()
          .includes(searchQuery.value.toLowerCase())
    );
  });

  // Helper function để lấy conversations của bot
  const getConversationsForBot = async (botUuid: string) => {
    try {
      const response = await getConversations({
        bot_uuid: botUuid,
        status: "active"
      });

      if (response.success && response.data) {
        conversations.value[botUuid] = response.data;
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching conversations for bot:", error);
      return [];
    }
  };

  // Helper function để load messages của conversation
  const loadConversationMessages = async (conversationId: string) => {
    try {
      const response = await getMessages(conversationId);

      if (response.success && response.data) {
        // Convert API messages to local format
        const formattedMessages = response.data.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content
        }));

        messages[conversationId] = formattedMessages;
        return formattedMessages;
      }
      return [];
    } catch (error) {
      console.error("Error loading conversation messages:", error);
      return [];
    }
  };

  // Helper function để kiểm tra và load conversation tồn tại
  const checkAndLoadExistingConversation = async (botUuid: string) => {
    try {
      // Lấy danh sách conversations của bot
      const botConversations = await getConversationsForBot(botUuid);

      if (botConversations.length > 0) {
        // Lấy conversation gần nhất (active)
        const latestConversation = botConversations[0];
        const conversationId = latestConversation.id.toString();

        // Set current conversation
        currentConversationId.value = conversationId;

        // Load messages của conversation này
        const conversationMessages =
          await loadConversationMessages(conversationId);

        // Nếu conversation trống, thêm greeting message
        if (conversationMessages.length === 0 && selectedAgent.value) {
          messages[conversationId] = [
            {
              id: `greeting-${conversationId}`,
              role: "assistant",
              content:
                selectedAgent.value.greetingMessage ||
                `Xin chào! Bạn cần tôi giúp gì với ${selectedAgent.value.name}?`
            }
          ];
        }

        console.log("Loaded existing conversation:", latestConversation);
        return conversationId;
      }

      return null; // Không có conversation nào
    } catch (error) {
      console.error("Error checking existing conversation:", error);
      return null;
    }
  };

  // Helper function để tạo conversation mới
  const createNewConversation = async (botUuid: string) => {
    try {
      // Gọi API để tạo conversation mới
      const response = await createConversation({
        title: `Cuộc trò chuyện với ${selectedAgent.value?.name || "Bot"}`,
        bot_uuid: botUuid
      });

      if (response.success && response.data) {
        const newConversationId = response.data.id.toString();
        currentConversationId.value = newConversationId;

        // Khởi tạo conversation với greeting message
        if (selectedAgent.value) {
          messages[newConversationId] = [
            {
              id: `greeting-${newConversationId}`,
              role: "assistant",
              content:
                selectedAgent.value.greetingMessage ||
                `Xin chào! Bạn cần tôi giúp gì với ${selectedAgent.value.name}?`
            }
          ];
        }

        console.log("Created new conversation:", response.data);
        return newConversationId;
      } else {
        throw new Error("Failed to create conversation");
      }
    } catch (error) {
      console.error("Error creating conversation:", error);
      ElMessage.error("Lỗi khi tạo cuộc trò chuyện mới");

      // Fallback: tạo ID local nếu API fail
      const fallbackId = `conv-${botUuid}-${Date.now()}`;
      currentConversationId.value = fallbackId;

      if (selectedAgent.value) {
        messages[fallbackId] = [
          {
            id: `greeting-${fallbackId}`,
            role: "assistant",
            content:
              selectedAgent.value.greetingMessage ||
              `Xin chào! Bạn cần tôi giúp gì với ${selectedAgent.value.name}?`
          }
        ];
      }

      return fallbackId;
    }
  };

  // API call để gửi tin nhắn
  const sendMessageToServer = async (prompt: string) => {
    try {
      if (!selectedAgent.value || !currentConversationId.value) {
        throw new Error("Không có agent hoặc conversation được chọn");
      }

      const response = await sendAndRespond({
        conversation_id: parseInt(currentConversationId.value),
        content: prompt
      });

      console.log("Message sent to server:", response);
      return response;
    } catch (error) {
      console.error("Error sending message to server:", error);
      ElMessage.error("Lỗi khi gửi tin nhắn đến server");
      throw error;
    }
  };

  // Trigger agent response
  const triggerAgentResponse = async (text: string) => {
    if (!selectedAgent.value || isTyping.value) return;

    isTyping.value = true;

    try {
      await sendMessageToServer(text);
      // Server sẽ xử lý và gửi response qua WebSocket
    } catch (error) {
      console.error("Error in triggerAgentResponse:", error);
      isTyping.value = false;
      ElMessage.error("Lỗi khi gửi tin nhắn");
    }
  };

  // Bắt đầu conversation mới khi cần thiết
  const ensureConversationExists = async () => {
    if (!selectedAgent.value) return null;

    // Nếu đã có conversation hiện tại, return luôn
    if (currentConversationId.value) {
      return currentConversationId.value;
    }

    // Tạo conversation mới
    console.log("Creating new conversation for first message");
    return await createNewConversation(selectedAgent.value.uuid);
  };

  // Gửi tin nhắn
  const sendMessage = async () => {
    const text = newMessage.value.trim();
    const files = attachedFiles.value;
    if ((!text && files.length === 0) || !selectedAgent.value) return;

    // Đảm bảo có conversation trước khi gửi tin nhắn
    const conversationId = await ensureConversationExists();
    if (!conversationId) {
      ElMessage.error("Không thể tạo cuộc trò chuyện");
      return;
    }

    let userMessageContent = text;
    if (files.length > 0) {
      userMessageContent += `\n(Đã đính kèm ${files.length} tệp)`;
    }

    // Thêm tin nhắn user vào conversation hiện tại
    messages[conversationId].push({
      id: Date.now(),
      role: "user",
      content: userMessageContent
    });

    const textToSend = newMessage.value.trim();
    newMessage.value = "";
    attachedFiles.value = [];

    await triggerAgentResponse(textToSend);
  };

  // Gửi starter prompt
  const sendStarterPrompt = async (promptText: string) => {
    if (!selectedAgent.value) return;

    // Đảm bảo có conversation trước khi gửi starter prompt
    const conversationId = await ensureConversationExists();
    if (!conversationId) {
      ElMessage.error("Không thể tạo cuộc trò chuyện");
      return;
    }

    messages[conversationId].push({
      id: Date.now(),
      role: "user",
      content: promptText
    });

    triggerAgentResponse(promptText);
  };

  // Xóa tin nhắn trong conversation (chỉ clear messages)
  const clearMessages = () => {
    if (!selectedAgent.value || !currentConversationId.value) return;

    console.log("clearMessages called - showing confirmation dialog");

    ElMessageBox.confirm(
      "Bạn có chắc chắn muốn xóa tất cả tin nhắn trong cuộc trò chuyện này không?",
      "Xác nhận xóa tin nhắn",
      {
        confirmButtonText: "Xóa tin nhắn",
        cancelButtonText: "Hủy",
        type: "warning"
      }
    )
      .then(() => {
        if (currentConversationId.value) {
          messages[currentConversationId.value] = [
            {
              id: `greeting-${currentConversationId.value}`,
              role: "assistant",
              content:
                selectedAgent.value.greetingMessage ||
                `Xin chào! Bạn cần tôi giúp gì với ${selectedAgent.value.name}?`
            }
          ];
        }
        ElMessage.success("Đã xóa tất cả tin nhắn");
      })
      .catch(() => {});
  };

  // Xóa hoàn toàn conversation
  const deleteCurrentConversation = () => {
    if (!selectedAgent.value || !currentConversationId.value) return;

    console.log(
      "deleteCurrentConversation called - showing confirmation dialog"
    );

    ElMessageBox.confirm(
      "Bạn có chắc chắn muốn xóa hoàn toàn cuộc trò chuyện này không? Hành động này không thể hoàn tác.",
      "Xác nhận xóa cuộc trò chuyện",
      {
        confirmButtonText: "Xóa cuộc trò chuyện",
        cancelButtonText: "Hủy",
        type: "error",
        dangerouslyUseHTMLString: false
      }
    )
      .then(async () => {
        try {
          if (currentConversationId.value) {
            // Gọi API để xóa conversation
            const response = await deleteConversation(
              currentConversationId.value
            );

            if (response.success) {
              // Xóa messages local
              delete messages[currentConversationId.value];

              // Tạo conversation mới
              if (selectedAgent.value) {
                await createNewConversation(selectedAgent.value.uuid);
              }

              ElMessage.success(
                "Đã xóa cuộc trò chuyện và tạo cuộc trò chuyện mới"
              );
            } else {
              throw new Error("Failed to delete conversation");
            }
          }
        } catch (error) {
          console.error("Error deleting conversation:", error);
          ElMessage.error("Lỗi khi xóa cuộc trò chuyện");
        }
      })
      .catch(() => {});
  };

  // Chọn agent
  const selectAgent = (agentId: string) => {
    chatBotStore.getSelectedChatBot(agentId);
  };

  // Xóa attachment
  const removeAttachment = (index: number) => {
    attachedFiles.value.splice(index, 1);
  };

  // WebSocket event handler cho bot response
  const handleBotResponse = (data: any) => {
    console.log("Received bot response:", data);

    // Validate dữ liệu nhận được
    if (!data || !data.content) {
      console.warn("Invalid bot response data:", data);
      isTyping.value = false;
      return;
    }

    // Nếu server gửi kèm conversationId, validate nó
    if (
      data.conversationId &&
      data.conversationId !== currentConversationId.value
    ) {
      console.warn(
        "Response for different conversation:",
        data.conversationId,
        "current:",
        currentConversationId.value
      );
      isTyping.value = false;
      return;
    }

    // Đảm bảo có conversation hiện tại
    if (!currentConversationId.value) {
      console.warn("No current conversation to add response to");
      isTyping.value = false;
      return;
    }

    // Thêm response vào conversation hiện tại
    messages[currentConversationId.value].push({
      id: data.messageId || Date.now() + 1,
      role: "assistant",
      content: data.content
    });

    // Tắt typing indicator
    isTyping.value = false;
  };

  // Watch selectedAgent để load conversation hiện có
  watch(
    selectedAgent,
    async newAgent => {
      if (newAgent) {
        console.log("Processing agent:", newAgent.uuid);

        // Luôn cố gắng load conversation hiện có trước
        const existingConversationId = await checkAndLoadExistingConversation(
          newAgent.uuid
        );

        // Chỉ tạo conversation mới nếu thực sự không có conversation nào
        // và người dùng chưa có conversation nào với bot này
        if (!existingConversationId) {
          console.log(
            "No existing conversation found for bot:",
            newAgent.uuid,
            "- User will need to start a new conversation manually"
          );
          // Không tự động tạo conversation mới
          // Để người dùng tự quyết định khi nào muốn bắt đầu chat
          currentConversationId.value = null;
        } else {
          console.log("Loaded existing conversation:", existingConversationId);
        }
      }
    },
    { immediate: true }
  );

  // Setup và cleanup WebSocket
  const setupWebSocket = () => {
    websocketService.on("bot.response", handleBotResponse);
  };

  const cleanupWebSocket = () => {
    websocketService.off("bot.response", handleBotResponse);
  };

  // Scroll to bottom function
  const scrollToBottom = (container?: HTMLElement) => {
    nextTick(() => {
      if (container) {
        container.scrollTo({ top: container.scrollHeight, behavior: "smooth" });
      }
    });
  };

  return {
    // State
    messages,
    conversations,
    currentConversationId,
    newMessage,
    isTyping,
    attachedFiles,
    searchQuery,
    filterType,

    // Computed
    selectedAgent,
    agents,
    currentMessages,
    showStarterPrompts,
    filteredAgents,

    // Methods
    createNewConversation,
    checkAndLoadExistingConversation,
    getConversationsForBot,
    loadConversationMessages,
    ensureConversationExists,
    sendMessage,
    sendStarterPrompt,
    clearMessages,
    deleteCurrentConversation,
    selectAgent,
    removeAttachment,
    triggerAgentResponse,
    sendMessageToServer,
    scrollToBottom,

    // WebSocket
    setupWebSocket,
    cleanupWebSocket,
    handleBotResponse
  };
}
