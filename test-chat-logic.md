# Test Chat Logic - Conversation Management

## Mục tiêu kiểm tra
Kiểm tra logic mới để đảm bảo:
1. Khi refresh page không tạo conversation mới
2. Ưu tiên conversation có sẵn trong bot data
3. Chỉ tạo conversation mới khi user thực sự gửi tin nhắn

## Test Cases

### Test Case 1: Bot có conversation sẵn trong data
**Kị<PERSON> bản**: Chọn bot đã có conversation trong data
**Kết quả mong đợi**: 
- Load conversation từ bot data
- Hiển thị messages hiện có
- Không tạo conversation mới

### Test Case 2: Bot không có conversation trong data
**Kịch bản**: Chọn bot không có conversation trong data
**Kết quả mong đợi**:
- Gọi API để tìm conversation
- Nếu có: load conversation đó
- Nếu không có: hiển thị welcome message, đợi user gửi tin nhắn

### Test Case 3: Refresh page
**Kị<PERSON> bản**: Refresh page khi đang chat
**Kết quả mong đợi**:
- Load lại conversation hiện có
- Không tạo conversation mới
- Giữ nguyên trạng thái chat

### Test Case 4: Gửi tin nhắn đầu tiên
**Kịch bản**: User gửi tin nhắn khi chưa có conversation
**Kết quả mong đợi**:
- Tự động tạo conversation mới
- Gửi tin nhắn thành công
- Conversation được lưu và sử dụng cho các tin nhắn tiếp theo

## Cách test

1. **Mở ứng dụng**: http://localhost:8849/
2. **Đăng nhập** (nếu cần)
3. **Vào trang chat**: /chat
4. **Kiểm tra console logs** để theo dõi logic:
   - "Found conversation in bot data"
   - "Loaded conversation from bot data"
   - "No existing conversation found"
   - "Creating new conversation for first message"

## ✅ Đã sửa lỗi trùng lặp greeting message

**Vấn đề**: Hiển thị 2 dòng greeting:
- "Xin chào! Tôi là Customer Support Assistant"
- "Xin chào! Tôi là trợ lý hỗ trợ khách hàng..."

**Giải pháp**:
- Loại bỏ greeting message tự động trong conversation
- Chỉ hiển thị welcome message trong UI
- Conversation bắt đầu trống, chỉ chứa messages thực từ user/bot

## Console Commands để test

```javascript
// Kiểm tra selectedAgent có conversation không
console.log('Selected Agent:', chatBot.selectedAgent.value);
console.log('Has conversation:', !!chatBot.selectedAgent.value?.conversation);

// Kiểm tra currentConversationId
console.log('Current Conversation ID:', chatBot.currentConversationId.value);

// Kiểm tra messages
console.log('Current Messages:', chatBot.currentMessages.value);

// Test tạo conversation mới
await chatBot.ensureConversationExists();
```

## Kết quả test

### ✅ Test Case 1: PASSED
- Bot với conversation sẵn load đúng
- Console log: "Found conversation in bot data"

### ✅ Test Case 2: PASSED  
- Bot không có conversation hiển thị welcome message
- Không tự động tạo conversation

### ✅ Test Case 3: PASSED
- Refresh page load lại conversation cũ
- Không tạo conversation mới

### ✅ Test Case 4: PASSED
- Gửi tin nhắn đầu tiên tạo conversation mới
- Logic ensureConversationExists hoạt động đúng

## Lưu ý
- Đảm bảo API trả về bot data có trường `conversation` khi có sẵn
- Kiểm tra WebSocket connection để nhận response
- Test với nhiều bot khác nhau (có và không có conversation)
